<?php
// Start output buffering
ob_start();

require_once 'config.php';
require_once 'database.php';
require_once 'auth.php';
require_once 'utilities.php';
require_once 'permissions.php';

// Initialize auth
$auth = new Auth();

// CSRF token generation
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
if (empty($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Check if user is logged in
if (!$auth->isLoggedIn() && basename($_SERVER['PHP_SELF']) !== 'login.php') {
    Utilities::redirect('login.php');
}

// Check session timeout
if (!$auth->checkSessionTimeout() && basename($_SERVER['PHP_SELF']) !== 'login.php') {
    Utilities::setFlashMessage('warning', 'Your session has expired. Please log in again.');
    Utilities::redirect('login.php');
}

// Skip permission check for login, logout, unauthorized pages
$noCheckPages = ['login.php', 'logout.php', 'unauthorized.php', 'index.php'];
$currentPage = basename($_SERVER['PHP_SELF']);

// Check if the current page requires permission
if (!in_array($currentPage, $noCheckPages)) {
    // Get the required permission for the current page
    $requiredPermission = getRequiredPermissionForCurrentPage();

    // If the page requires permission, check if the user has it
    if ($requiredPermission !== null) {
        // For staff members, check specific permissions
        if ($_SESSION['role'] === 'staff') {
            if (!hasPermission($requiredPermission)) {
                redirectToUnauthorized();
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo APP_NAME; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">

    <!-- Google Fonts - Inter -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap">

    <!-- Modern CSS -->
    <link rel="stylesheet" href="assets/css/modern.css">

    <!-- Sidebar Sections CSS -->
    <link rel="stylesheet" href="assets/css/sidebar-sections.css">

    <!-- Admin Table CSS -->
    <link rel="stylesheet" href="assets/css/admin-table.css">

    <!-- Fixed Sidebar CSS - Always keep sidebar visible -->
    <link rel="stylesheet" href="assets/css/fixed-sidebar.css">

    <!-- Global Dropdown Fix CSS -->
    <link rel="stylesheet" href="assets/css/global-dropdown-fix.css">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" />

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="assets/img/favicon.svg">
    <link rel="alternate icon" href="assets/img/favicon.ico" type="image/x-icon">

    <!-- Responsive Dashboard CSS -->
    <link rel="stylesheet" href="assets/css/responsive-dashboard.css">
</head>
<body>
    <!-- Wrapper -->
    <div id="wrapper">
        <!-- Sidebar -->
        <div id="sidebar-wrapper">
            <div class="sidebar-heading">
                <h4>Admin Panel</h4>
                <button class="sidebar-close" id="sidebarClose">
                    <i class="fas fa-times"></i>
                </button>
                <button class="sidebar-collapse-toggle d-none d-lg-flex" id="sidebarCollapseToggle" title="Collapse Sidebar">
                    <i class="fas fa-chevron-left"></i>
                </button>
            </div>
            <div class="sidebar-menu">
                <!-- DASHBOARD Section -->
                <div class="sidebar-section">
                    <div class="sidebar-section-header">DASHBOARD</div>
                    <a href="index.php" class="sidebar-menu-item <?php echo basename($_SERVER['PHP_SELF']) == 'index.php' ? 'active' : ''; ?>">
                        <i class="fas fa-tachometer-alt"></i> <span>Overview Dashboard</span>
                    </a>
                </div>

                <!-- PLATFORM CONTROL Section -->
                <?php if ($auth->hasRole('super_admin')): ?>
                <div class="sidebar-section">
                    <div class="sidebar-section-header">PLATFORM CONTROL</div>
                    <a href="staff_management.php" class="sidebar-menu-item <?php echo basename($_SERVER['PHP_SELF']) == 'staff_management.php' ? 'active' : ''; ?>">
                        <i class="fas fa-user-shield"></i> <span>Staff Management</span>
                    </a>
                    <a href="audit_logs.php" class="sidebar-menu-item <?php echo basename($_SERVER['PHP_SELF']) == 'audit_logs.php' ? 'active' : ''; ?>">
                        <i class="fas fa-history"></i> <span>Audit Logs</span>
                    </a>
                    <a href="settings.php" class="sidebar-menu-item <?php echo basename($_SERVER['PHP_SELF']) == 'settings.php' ? 'active' : ''; ?>">
                        <i class="fas fa-cog"></i> <span>Platform Settings</span>
                    </a>
                </div>
                <?php endif; ?>

                <!-- INSIGHTS & REPORTS Section -->
                <?php if ($auth->hasRole('super_admin') || $auth->hasRole('admin') || hasPermission('view_reports')): ?>
                <div class="sidebar-section">
                    <div class="sidebar-section-header">INSIGHTS & REPORTS</div>
                    <a href="reports.php" class="sidebar-menu-item <?php echo basename($_SERVER['PHP_SELF']) == 'reports.php' ? 'active' : ''; ?>">
                        <i class="fas fa-chart-bar"></i> <span>Reports</span>
                    </a>
                </div>
                <?php endif; ?>

                <!-- USER OPERATIONS Section -->
                <?php if ($auth->hasRole('super_admin') || $auth->hasRole('admin') || hasPermission('manage_users')): ?>
                <div class="sidebar-section">
                    <div class="sidebar-section-header">USER OPERATIONS</div>
                    <a href="users.php" class="sidebar-menu-item <?php echo basename($_SERVER['PHP_SELF']) == 'users.php' ? 'active' : ''; ?>">
                        <i class="fas fa-users"></i> <span>All Users</span>
                    </a>
                    <?php if ($auth->hasRole('super_admin') || $auth->hasRole('admin') || hasPermission('assign_users')): ?>
                    <a href="course_assign.php" class="sidebar-menu-item <?php echo basename($_SERVER['PHP_SELF']) == 'course_assign.php' ? 'active' : ''; ?>">
                        <i class="fas fa-user-plus"></i> <span>Assign Courses</span>
                    </a>
                    <?php endif; ?>
                </div>
                <?php endif; ?>

                <!-- COURSES Section -->
                <?php if ($auth->hasRole('super_admin') || $auth->hasRole('admin') || hasPermission('manage_courses')): ?>
                <div class="sidebar-section">
                    <div class="sidebar-section-header">COURSES</div>
                    <a href="courses.php" class="sidebar-menu-item <?php echo basename($_SERVER['PHP_SELF']) == 'courses.php' ? 'active' : ''; ?>">
                        <i class="fas fa-list"></i> <span>Course List</span>
                    </a>
                    <a href="course_add.php" class="sidebar-menu-item <?php echo basename($_SERVER['PHP_SELF']) == 'course_add.php' ? 'active' : ''; ?>">
                        <i class="fas fa-plus"></i> <span>Create Course</span>
                    </a>
                </div>
                <?php endif; ?>

                <!-- ENGAGEMENT Section -->
                <div class="sidebar-section">
                    <div class="sidebar-section-header">ENGAGEMENT</div>
                    <?php if ($auth->hasRole('super_admin') || $auth->hasRole('admin') || hasPermission('manage_settings')): ?>
                    <!-- Water reminders removed as requested -->
                    <?php endif; ?>
                </div>

                <!-- ADVANCED TOOLS Section -->
                <?php if ($auth->hasRole('super_admin') || $auth->hasRole('admin')): ?>
                <div class="sidebar-section">
                    <div class="sidebar-section-header">ADVANCED TOOLS</div>
                    <?php if ($auth->hasRole('super_admin')): ?>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Page Content -->
        <div id="page-content-wrapper">
            <!-- Top Navigation -->
            <div class="top-navbar d-flex align-items-center justify-content-between">
                <button class="navbar-toggler" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="flex-grow-1 d-flex align-items-center">
                    <!-- Development Mode badge removed -->
                </div>
                <?php if (
                    $auth->hasRole('super_admin') ||
                    $auth->hasRole('manager') ||
                    $auth->hasRole('admin') ||
                    $auth->hasRole('staff')
                ): ?>
                <div class="d-flex align-items-center justify-content-center me-3" style="height: 42px; width: 42px;">
                    <a href="notifications.php" class="notification-bell d-flex align-items-center justify-content-center" title="Notifications">
                        <i class="fas fa-bell"></i>
                        <!-- Optionally, add a badge for unread notifications -->
                        <!-- <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">3</span> -->
                    </a>
                </div>
                <?php endif; ?>
                <div class="user-dropdown ms-auto">
                        <div class="user-dropdown-toggle" id="userDropdownToggle">
                            <div class="navbar-user-info d-none d-md-block">
                                <div class="navbar-user-name">
                                    <?php echo isset($_SESSION['name']) ? htmlspecialchars($_SESSION['name']) : (isset($_SESSION['username']) ? htmlspecialchars($_SESSION['username']) : 'User'); ?>
                                </div>
                                <div class="navbar-user-role">
                                    <?php
                                    if ($auth->hasRole('super_admin')) {
                                        echo 'Super Admin';
                                    } elseif ($auth->hasRole('admin')) {
                                        echo 'Administrator';
                                    } elseif ($auth->hasRole('staff')) {
                                        echo 'Staff';
                                    } elseif ($auth->hasRole('editor')) {
                                        echo 'Editor';
                                    } elseif ($auth->hasRole('viewer')) {
                                        echo 'Viewer';
                                    } else {
                                        echo 'User';
                                    }
                                    ?>
                                </div>
                            </div>
                            <a href="profile.php" class="navbar-user-avatar ms-2" title="View Profile">
                                <?php
                                $username = isset($_SESSION['username']) ? $_SESSION['username'] : 'U';
                                echo strtoupper(substr($username, 0, 1));
                                ?>
                            </a>
                        </div>
                        <div class="user-dropdown-menu d-none" id="userDropdownMenu">
                            <a href="profile.php" class="user-dropdown-item <?php echo (isset($activePage) && $activePage === 'profile.php') || basename($_SERVER['PHP_SELF']) === 'profile.php' ? 'active' : ''; ?>">
                                <i class="fas fa-user me-2"></i> Profile
                            </a>
                            <div class="user-dropdown-divider"></div>
                            <a href="logout.php" class="user-dropdown-item">
                                <i class="fas fa-sign-out-alt me-2"></i> Logout
                            </a>
                    </div>
                </div>
            </div>

            <!-- Flash Messages -->
            <div class="container-fluid px-4 pt-4">
                <?php Utilities::displayFlashMessages(); ?>
            </div>

            <!-- Main Content Container -->
            <div class="container-fluid p-4"><?php // Content goes here ?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const sidebarClose = document.getElementById('sidebarClose');
    const wrapper = document.getElementById('wrapper');
    const sidebarWrapper = document.getElementById('sidebar-wrapper');
    const pageContent = document.getElementById('page-content-wrapper');
    
    // Function to handle sidebar state
    function toggleSidebar(show) {
        if (show) {
            wrapper.classList.add('toggled');
            sidebarWrapper.style.transform = 'translateX(0)';
            pageContent.style.marginLeft = '280px';
            pageContent.style.width = 'calc(100% - 280px)';
        } else {
            wrapper.classList.remove('toggled');
            sidebarWrapper.style.transform = 'translateX(-280px)';
            pageContent.style.marginLeft = '0';
            pageContent.style.width = '100%';
        }
    }
    
    // Close button click handler
    sidebarClose.addEventListener('click', function() {
        toggleSidebar(false);
    });
    
    // Toggle button click handler (if exists)
    const sidebarToggle = document.getElementById('sidebarToggle');
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            toggleSidebar(!wrapper.classList.contains('toggled'));
        });
    }
});
</script>
