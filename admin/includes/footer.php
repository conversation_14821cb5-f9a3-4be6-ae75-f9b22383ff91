            </div> <!-- End of Main Content Container -->

            <!-- Footer -->
            <footer class="footer mt-auto py-3 bg-white border-top">
                <div class="container-fluid px-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="text-muted">
                            &copy; <?php echo date('Y'); ?> <?php echo APP_NAME; ?>. All rights reserved.
                        </div>
                        <div class="text-muted">
                            Version 1.0
                        </div>
                    </div>
                </div>
            </footer>
        </div> <!-- End of Page Content Wrapper -->
    </div> <!-- End of Wrapper -->

    <!-- Bootstrap JS Bundle with <PERSON>per -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <!-- Custom JS -->
    <script src="assets/js/scripts.js"></script>

    <!-- Select2 Initialization -->
    <script src="assets/js/select2-init.js"></script>

    <!-- Real-time Updates -->
    <script src="assets/js/realtime-updates.js"></script>

    <?php
    // Page-specific JavaScript files
    $currentPage = basename($_SERVER['PHP_SELF']);
    if ($currentPage === 'user_add.php'):
    ?>
    <script src="assets/js/user-add.js"></script>
    <?php endif; ?>

    <script>
        // Sidebar toggle button - now just for visual feedback, doesn't actually hide sidebar
        document.addEventListener('DOMContentLoaded', function() {
            const sidebarToggle = document.getElementById('sidebarToggle');
            const wrapper = document.getElementById('wrapper');
            const pageContent = document.getElementById('page-content-wrapper');
            const sidebar = document.getElementById('sidebar-wrapper');

            // Hide the sidebar toggle button since we don't need it anymore
            if (sidebarToggle) {
                sidebarToggle.style.display = 'none';
            }

            // Always ensure the sidebar is visible and wrapper is toggled
            if (wrapper) {
                wrapper.classList.add('toggled');
                localStorage.setItem('sidebarToggled', 'true');
            }

            if (sidebar) {
                sidebar.style.transform = 'translateX(0)';
                sidebar.style.visibility = 'visible';
                sidebar.style.opacity = '1';
            }

            if (pageContent) {
                pageContent.style.marginLeft = '280px';
                pageContent.style.width = 'calc(100% - 280px)';
            }

            console.log('Sidebar permanently visible');
        });

        // Mobile close button - disabled since we want sidebar to always be visible
        document.addEventListener('DOMContentLoaded', function() {
            const mobileClose = document.getElementById('mobile-close');

            // Hide the mobile close button since we don't need it anymore
            if (mobileClose) {
                mobileClose.style.display = 'none';
            }
        });

        // Disabled: No longer closing sidebar when clicking outside
        // The sidebar should always remain visible

        // Always show sidebar regardless of localStorage state
        document.addEventListener('DOMContentLoaded', function() {
            try {
                const wrapper = document.getElementById('wrapper');
                const pageContent = document.getElementById('page-content-wrapper');
                const sidebarToggle = document.getElementById('sidebarToggle');
                const sidebar = document.getElementById('sidebar-wrapper');

                // Always set toggled state
                wrapper.classList.add('toggled');
                localStorage.setItem('sidebarToggled', 'true');

                // Ensure content is properly positioned
                if (pageContent) {
                    pageContent.style.marginLeft = '280px';
                    pageContent.style.width = 'calc(100% - 280px)';
                }

                // Ensure the sidebar is visible
                if (sidebar) {
                    sidebar.style.transform = 'translateX(0)';
                    sidebar.style.visibility = 'visible';
                    sidebar.style.opacity = '1';
                }

                // Ensure the hamburger icon is visible if it exists
                if (sidebarToggle) {
                    sidebarToggle.style.visibility = 'visible';
                    sidebarToggle.style.opacity = '1';
                }

                console.log('Sidebar always visible');
            } catch (error) {
                console.error('Error setting sidebar state:', error);
            }
        });

        // Handle window resize - always keep sidebar visible
        window.addEventListener('resize', function() {
            const wrapper = document.getElementById('wrapper');
            const pageContent = document.getElementById('page-content-wrapper');
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebar = document.getElementById('sidebar-wrapper');

            // Always ensure toggled state
            wrapper.classList.add('toggled');

            // Always ensure sidebar is visible regardless of screen size
            if (sidebar) {
                sidebar.style.transform = 'translateX(0)';
                sidebar.style.visibility = 'visible';
                sidebar.style.opacity = '1';
            }

            // Always ensure content is properly positioned
            if (pageContent) {
                pageContent.style.marginLeft = '280px';
                pageContent.style.width = 'calc(100% - 280px)';
            }

            // Ensure the hamburger icon is visible if needed
            if (sidebarToggle) {
                sidebarToggle.style.visibility = 'visible';
                sidebarToggle.style.opacity = '1';
                sidebarToggle.style.zIndex = '1060';
            }
        });

        // Handle sidebar menu item clicks
        document.addEventListener('DOMContentLoaded', function() {
            const sidebarMenuItems = document.querySelectorAll('.sidebar-menu-item');

            // Check if there's a stored active menu item
            const activeMenuItem = localStorage.getItem('activeMenuItem');

            if (activeMenuItem) {
                // Remove active class from all menu items first
                sidebarMenuItems.forEach(item => {
                    if (!item.classList.contains('active')) {
                        item.classList.remove('clicked-active');
                    }
                });

                // Add active class to the stored menu item
                const storedItem = document.querySelector(activeMenuItem);
                if (storedItem && !storedItem.classList.contains('active')) {
                    storedItem.classList.add('clicked-active');
                }
            }

            // Add click event listeners to all menu items
            sidebarMenuItems.forEach(item => {
                // Skip items with data-bs-toggle attribute (dropdown toggles)
                if (!item.hasAttribute('data-bs-toggle')) {
                    item.addEventListener('click', function(e) {
                        // Store current scroll position
                        const currentScrollY = window.scrollY;

                        // Only for items that don't have the 'active' class (not current page)
                        if (!this.classList.contains('active')) {
                            // Store this item's selector in localStorage
                            const href = this.getAttribute('href');

                            // Only store if it's a real link (not a # link)
                            if (href && href !== '#' && !href.startsWith('javascript:')) {
                                const selector = this.tagName.toLowerCase() +
                                                '[href="' + href + '"]';
                                localStorage.setItem('activeMenuItem', selector);

                                // If this is a submenu item, also store the parent menu state
                                const submenu = this.closest('.sidebar-submenu');
                                if (submenu) {
                                    const parentId = submenu.closest('.collapse').id;
                                    const parentToggle = document.querySelector('[data-bs-toggle="collapse"][href="#' + parentId + '"]');
                                    if (parentToggle) {
                                        localStorage.setItem('expandedSubmenu', parentId);
                                    }
                                }

                                // Remove clicked-active class from all items
                                sidebarMenuItems.forEach(menuItem => {
                                    if (!menuItem.classList.contains('active')) {
                                        menuItem.classList.remove('clicked-active');
                                    }
                                });

                                // Add clicked-active class to this item
                                this.classList.add('clicked-active');

                                // Allow navigation to proceed normally
                                // The browser will handle the page navigation
                            }
                        } else {
                            // If clicking on the current page, prevent default and maintain scroll position
                            e.preventDefault();
                            window.scrollTo(0, currentScrollY);
                        }
                    });
                } else {
                    // For dropdown toggles, store their state when clicked
                    item.addEventListener('click', function(e) {
                        const target = this.getAttribute('href') || this.getAttribute('data-bs-target');
                        if (target && target.startsWith('#')) {
                            const submenuId = target.substring(1);
                            const isExpanded = this.getAttribute('aria-expanded') === 'true';

                            if (isExpanded) {
                                localStorage.setItem('expandedSubmenu', submenuId);
                            } else {
                                localStorage.removeItem('expandedSubmenu');
                            }
                        }
                    });
                }
            });

            // Check if there's a stored expanded submenu
            const expandedSubmenu = localStorage.getItem('expandedSubmenu');
            if (expandedSubmenu) {
                const submenu = document.getElementById(expandedSubmenu);
                const toggle = document.querySelector('[data-bs-toggle="collapse"][href="#' + expandedSubmenu + '"]');

                if (submenu && toggle && !submenu.classList.contains('show')) {
                    // Use Bootstrap's collapse API to show the submenu
                    const bsCollapse = new bootstrap.Collapse(submenu, {
                        toggle: false
                    });
                    bsCollapse.show();

                    // Update the toggle button state
                    toggle.setAttribute('aria-expanded', 'true');
                    toggle.classList.add('active');
                }
            }

            // Function to clear active menu state (for debugging)
            window.clearActiveMenuState = function() {
                localStorage.removeItem('activeMenuItem');
                localStorage.removeItem('expandedSubmenu');

                // Remove clicked-active class from all items
                document.querySelectorAll('.sidebar-menu-item').forEach(item => {
                    item.classList.remove('clicked-active');
                });

                // Close all expanded submenus that aren't on the current page
                document.querySelectorAll('.collapse.show').forEach(submenu => {
                    const toggle = document.querySelector('[data-bs-toggle="collapse"][href="#' + submenu.id + '"]');
                    if (toggle && !toggle.classList.contains('active')) {
                        const bsCollapse = new bootstrap.Collapse(submenu);
                        bsCollapse.hide();
                    }
                });

                console.log('Active menu state cleared');
                return false;
            };
        });

        // User dropdown toggle
        document.getElementById('userDropdownToggle').addEventListener('click', function(e) {
            e.preventDefault();
            document.getElementById('userDropdownMenu').classList.toggle('d-none');
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            const dropdown = document.getElementById('userDropdownMenu');
            const toggle = document.getElementById('userDropdownToggle');
            if (!toggle.contains(e.target) && !dropdown.contains(e.target)) {
                dropdown.classList.add('d-none');
            }
        });

        // Auto-hide alerts after 10 seconds (except for alerts with .alert-persistent class)
        window.setTimeout(function() {
            $(".alert:not(.alert-persistent)").fadeTo(500, 0).slideUp(500, function() {
                $(this).remove();
            });
        }, 10000);
    </script>
    <script src="assets/js/modern-action-menu.js"></script>
</body>
</html>
<?php
// Flush the output buffer
if (ob_get_level()) {
    ob_end_flush();
}
// End of file
?>
