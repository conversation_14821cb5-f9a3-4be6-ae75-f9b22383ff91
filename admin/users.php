<?php
require_once 'includes/header.php';
require_once 'includes/user_helpers.php';

// Load modern action menu CSS
echo '<link rel="stylesheet" href="assets/css/modern-action-menu.css">';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Check if user is logged in
if (!$auth->isLoggedIn()) {
    Utilities::setFlashMessage('error', 'You must be logged in to access this page.');
    Utilities::redirect('login.php');
}

// Check if user has admin role for full access
$isAdmin = $auth->hasRole('admin') || $auth->hasRole('super_admin');
$isSuperAdmin = $auth->hasRole('super_admin');
$isStaff = $auth->hasRole('staff');

// Get current admin role and ID
$currentAdminRole = $auth->getUserRole();
$currentAdminId = $auth->getUserId();

// Handle user deletion (admin only)
if ($isAdmin && isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $userId = (int)$_GET['delete'];

    // Don't allow deleting the admin user (ID 1)
    if ($userId == 1) {
        Utilities::setFlashMessage('error', 'Cannot delete the admin user.');
        Utilities::redirect('users.php');
    }

    $deleteQuery = "DELETE FROM users WHERE id = ? AND id != 1";
    $stmt = $conn->prepare($deleteQuery);
    $stmt->bind_param("i", $userId);

    if ($stmt->execute()) {
        Utilities::setFlashMessage('success', 'User has been deleted successfully.');
    } else {
        Utilities::setFlashMessage('error', 'Failed to delete user.');
    }

    Utilities::redirect('users.php');
}

// Handle user status toggle (admin only)
if ($isAdmin && isset($_GET['action']) && isset($_GET['id']) && is_numeric($_GET['id'])) {
    $action = $_GET['action'];
    $userId = (int)$_GET['id'];

    // Don't allow deactivating the admin user (ID 1)
    if ($userId == 1 && $action == 'deactivate') {
        Utilities::setFlashMessage('error', 'Cannot deactivate the admin user.');
        Utilities::redirect('users.php');
    }

    if ($action == 'activate') {
        $query = "UPDATE users SET is_active = 1 WHERE id = ? AND id != 1";
        $successMessage = "User activated successfully.";
        $errorMessage = "Failed to activate user.";
    } elseif ($action == 'deactivate') {
        $query = "UPDATE users SET is_active = 0 WHERE id = ? AND id != 1";
        $successMessage = "User deactivated successfully.";
        $errorMessage = "Failed to deactivate user.";
    } else {
        Utilities::redirect('users.php');
    }

    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $userId);

    if ($stmt->execute()) {
        Utilities::setFlashMessage('success', $successMessage);
    } else {
        Utilities::setFlashMessage('error', $errorMessage);
    }

    Utilities::redirect('users.php');
}

// Handle bulk actions (admin only)
if ($isAdmin && isset($_POST['bulk_action']) && isset($_POST['user_ids']) && !empty($_POST['bulk_action'])) {
    $action = $_POST['bulk_action'];
    $userIds = $_POST['user_ids'] ?? [];

    if (!empty($userIds)) {
        $successCount = 0;
        $errorCount = 0;

        foreach ($userIds as $userId) {
            if (!is_numeric($userId)) continue;

            // Skip admin user for certain actions
            if ($userId == 1 && ($action == 'delete' || $action == 'deactivate')) {
                $errorCount++;
                continue;
            }

            switch ($action) {
                case 'delete':
                    $query = "DELETE FROM users WHERE id = ? AND id != 1";
                    break;
                case 'activate':
                    $query = "UPDATE users SET is_active = 1 WHERE id = ? AND id != 1";
                    break;
                case 'deactivate':
                    $query = "UPDATE users SET is_active = 0 WHERE id = ? AND id != 1";
                    break;
                default:
                    continue 2; // Skip to next user ID
            }

            $stmt = $conn->prepare($query);
            $stmt->bind_param("i", $userId);

            if ($stmt->execute()) {
                $successCount++;
            } else {
                $errorCount++;
            }
        }

        if ($successCount > 0) {
            $actionText = ucfirst(str_replace(['_', '-'], ' ', $action));
            Utilities::setFlashMessage('success', "$actionText action completed successfully for $successCount user(s).");
        }

        if ($errorCount > 0) {
            Utilities::setFlashMessage('error', "Failed to process $errorCount user(s).");
        }

        Utilities::redirect('users.php');
    }
}

// Get filter parameters
$search = isset($_GET['search']) ? Utilities::sanitizeInput($_GET['search']) : '';
$status = isset($_GET['status']) ? Utilities::sanitizeInput($_GET['status']) : '';
$verification = isset($_GET['verification']) ? Utilities::sanitizeInput($_GET['verification']) : '';
$dateRange = isset($_GET['date_range']) ? Utilities::sanitizeInput($_GET['date_range']) : '';
$sort = isset($_GET['sort']) ? Utilities::sanitizeInput($_GET['sort']) : 'created_at';
$order = isset($_GET['order']) ? Utilities::sanitizeInput($_GET['order']) : 'DESC';
$staffId = isset($_GET['staff_id']) ? (int)$_GET['staff_id'] : '';

// Get all staff members for the filter dropdown (for super admins)
$staffMembers = [];
if ($isSuperAdmin) {
    $staffMembers = getAllStaffMembers($conn);
}

// Create filters array for the helper function
$filters = [
    'search' => $search,
    'status' => $status,
    'verification' => $verification,
    'date_range' => $dateRange,
    'sort' => $sort,
    'order' => $order,
    'staff_id' => $staffId
];

// Add pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 15;
$offset = ($page - 1) * $limit;

// Get total count for pagination
$totalUsers = getUsersWithFilters($conn, $auth, $filters, true);
$totalPages = ceil($totalUsers / $limit);

// Get users with pagination
$users = getUsersWithFilters($conn, $auth, $filters, false, $limit, $offset);

// Get user statistics
$userStats = getUserStatistics($conn, $auth, $filters);

// Calculate Active This Month
$activeThisMonth = 0;
$activeThisMonthPercent = 0;
if (!empty($users)) {
    $now = new DateTime();
    $monthAgo = (clone $now)->modify('-30 days');
    $activeThisMonth = 0;
    foreach ($users as $user) {
        if (!empty($user['last_login']) && (new DateTime($user['last_login'])) >= $monthAgo) {
            $activeThisMonth++;
        }
    }
    $activeThisMonthPercent = $userStats['total'] > 0 ? round(($activeThisMonth / $userStats['total']) * 100) : 0;
}

// Debug the result
error_log("Number of users found: " . count($users));
?>

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <div>
        <h1 class="h3 mb-0 text-gray-800 font-weight-bold">App Users</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb bg-transparent p-0 mb-0">
                <li class="breadcrumb-item"><a href="index.php">Dashboard</a></li>
                <li class="breadcrumb-item active" aria-current="page">App Users</li>
            </ol>
        </nav>
    </div>
    <?php if ($isAdmin || $isStaff): ?>
    <div class="d-flex">
        <?php if ($isAdmin): ?>
        <button type="button" class="btn minimal-export-btn" id="exportUsersBtn" title="Export as CSV">
            <i class="fas fa-file-csv"></i>
        </button>
        <?php endif; ?>
    </div>
    <?php endif; ?>
</div>

<?php if ($isAdmin || $isStaff): ?>
<!-- Floating Action Button (FAB) for Add New User -->
<a href="user_add.php<?php echo $isStaff ? '?assigned_staff_id=' . $currentAdminId : ''; ?>" class="fab-add-user" title="Add New User">
    <i class="fas fa-plus"></i>
</a>
<style>
.fab-add-user {
    position: fixed;
    bottom: 32px;
    right: 32px;
    width: 60px;
    height: 60px;
    background: #27ae60;
    color: #fff;
    border-radius: 50%;
    box-shadow: 0 4px 16px rgba(39, 174, 96, 0.18);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    z-index: 1050;
    transition: background 0.2s, box-shadow 0.2s;
    text-decoration: none;
}
.fab-add-user:hover, .fab-add-user:focus {
    background: #219150;
    color: #fff;
    box-shadow: 0 8px 24px rgba(39, 174, 96, 0.28);
    text-decoration: none;
}
@media (max-width: 600px) {
    .fab-add-user {
        width: 48px;
        height: 48px;
        font-size: 1.5rem;
        bottom: 20px;
        right: 20px;
    }
}
</style>
<?php endif; ?>

<?php Utilities::displayFlashMessages(); ?>

<!-- User Statistics Dashboard -->
<?php if ($isAdmin || $isStaff): ?>
<style>
.stats-minimal-card {
    border: none !important;
    box-shadow: none !important;
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1rem 1.2rem 0.8rem 1.2rem;
    margin-bottom: 1rem;
    min-height: 90px;
}
.stats-minimal-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    margin-right: 0.7rem;
    background: #e9ecef;
}
.stats-minimal-title {
    font-size: 0.98rem;
    font-weight: 500;
    color: #888;
    margin-bottom: 0.2rem;
}
.stats-minimal-value {
    font-size: 1.25rem;
    font-weight: 600;
    color: #222;
}
.stats-minimal-desc {
    font-size: 0.85rem;
    color: #aaa;
    margin-top: 0.2rem;
}
</style>
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-2">
        <div class="stats-minimal-card">
            <div class="d-flex align-items-center mb-1">
                <div class="stats-minimal-icon bg-primary text-white">
                        <i class="fas fa-users"></i>
                    </div>
                <div>
                    <div class="stats-minimal-title">Total Users</div>
                    <div class="stats-minimal-value"><?php echo number_format($userStats['total']); ?></div>
                </div>
                </div>
            <div class="stats-minimal-desc">
                <?php if ($isStaff): ?>Users assigned to you<?php elseif ($isSuperAdmin && !empty($staffId)): ?>Users for selected staff<?php else: ?>All app users<?php endif; ?>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-2">
        <div class="stats-minimal-card">
            <div class="d-flex align-items-center mb-1">
                <div class="stats-minimal-icon bg-success text-white">
                        <i class="fas fa-user-check"></i>
                    </div>
                <div>
                    <div class="stats-minimal-title">Active</div>
                    <div class="stats-minimal-value"><?php echo number_format($userStats['active']); ?></div>
                </div>
                </div>
            <div class="stats-minimal-desc"><?php echo $userStats['active_percentage']; ?>% active</div>
            </div>
        </div>
    <div class="col-xl-3 col-md-6 mb-2">
        <div class="stats-minimal-card">
            <div class="d-flex align-items-center mb-1">
                <div class="stats-minimal-icon bg-info text-white">
                    <i class="fas fa-bolt"></i>
    </div>
                <div>
                    <div class="stats-minimal-title">Active This Month</div>
                    <div class="stats-minimal-value"><?php echo number_format($activeThisMonth); ?> users</div>
                    </div>
                </div>
            <div class="stats-minimal-desc"><?php echo $activeThisMonthPercent; ?>% of all users</div>
                </div>
            </div>
    <div class="col-xl-3 col-md-6 mb-2">
        <div class="stats-minimal-card">
            <div class="d-flex align-items-center mb-1">
                <div class="stats-minimal-icon bg-info text-white">
                        <i class="fas fa-user-plus"></i>
                    </div>
                <div>
                    <div class="stats-minimal-title">New (7d)</div>
                    <div class="stats-minimal-value"><?php echo number_format($userStats['new']); ?></div>
                </div>
            </div>
            <div class="stats-minimal-desc">Last 7 days</div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Filters and Search -->
<?php if ($isAdmin): ?>
<style>
.filters-minimal-card {
    border: none !important;
    box-shadow: none !important;
    background: #f8f9fa;
    border-radius: 10px;
    padding: 0.7rem 1.2rem 0.7rem 1.2rem;
    margin-bottom: 1.2rem;
}
.filters-minimal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem 0 0.5rem 0;
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
    position: sticky;
    top: 0;
    z-index: 10;
}
.filters-minimal-header h6 {
    font-size: 1rem;
    font-weight: 600;
    color: #27ae60;
    margin: 0;
    display: flex;
    align-items: center;
}
.filters-minimal-header i {
    font-size: 1.1rem;
    margin-right: 0.5rem;
}
.filters-minimal-form .form-label {
    font-size: 0.92rem;
    color: #888;
    margin-bottom: 0.2rem;
}
.filters-minimal-form .form-control, .filters-minimal-form .form-select {
    font-size: 0.95rem;
    padding: 0.35rem 0.7rem;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
    background: #fff;
}
.filters-minimal-form .input-group-text {
    background: #f3f3f3;
    border: 1px solid #e0e0e0;
    font-size: 1rem;
}
.filters-minimal-form .btn {
    font-size: 0.95rem;
    border-radius: 6px;
    padding: 0.35rem 1.1rem;
}
.filters-minimal-form .btn-success {
    background: #27ae60 !important;
    border: none !important;
}
.filters-minimal-form .btn-success:hover {
    background: #219150 !important;
}
</style>
<div class="card filters-minimal-card mb-4">
    <div class="filters-minimal-header">
        <h6><i class="fas fa-filter me-2"></i> Filters</h6>
        <button class="btn btn-sm btn-link text-primary p-0" type="button" data-bs-toggle="collapse" data-bs-target="#filtersCollapse" aria-expanded="false" aria-controls="filtersCollapse" style="font-size:1.1rem;"><i class="fas fa-chevron-down"></i></button>
        </div>
    <div class="collapse" id="filtersCollapse">
        <div class="card-body pb-2 pt-3 filters-minimal-form">
            <form action="users.php" method="get" class="row g-2 align-items-end">
                <div class="col-lg-4 col-md-6">
                    <div class="form-group mb-2">
                        <label for="search" class="form-label">Search</label>
                        <div class="input-group">
                            <span class="input-group-text bg-light border-end-0">
                                <i class="fas fa-search text-muted"></i>
                            </span>
                            <input type="text" class="form-control border-start-0 ps-0" id="search" name="search"
                                   placeholder="Name, username or phone..." value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                    </div>
                </div>
                <div class="col-lg-2 col-md-3 col-sm-6">
                    <div class="form-group mb-2">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">All</option>
                            <option value="active" <?php echo $status === 'active' ? 'selected' : ''; ?>>Active</option>
                            <option value="inactive" <?php echo $status === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                        </select>
                    </div>
                </div>
                <div class="col-lg-2 col-md-3 col-sm-6">
                    <div class="form-group mb-2">
                        <label for="verification" class="form-label">Verification</label>
                        <select class="form-select" id="verification" name="verification">
                            <option value="">All</option>
                            <option value="verified" <?php echo isset($_GET['verification']) && $_GET['verification'] === 'verified' ? 'selected' : ''; ?>>Verified</option>
                            <option value="unverified" <?php echo isset($_GET['verification']) && $_GET['verification'] === 'unverified' ? 'selected' : ''; ?>>Unverified</option>
                        </select>
                    </div>
                </div>
                <?php if ($isSuperAdmin && !empty($staffMembers)): ?>
                <div class="col-lg-2 col-md-3 col-sm-6">
                    <div class="form-group mb-2">
                        <label for="staff_id" class="form-label">Staff</label>
                        <select class="form-select" id="staff_id" name="staff_id">
                            <option value="">All</option>
                            <?php foreach ($staffMembers as $staff): ?>
                            <option value="<?php echo $staff['id']; ?>" <?php echo $staffId == $staff['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($staff['name']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <?php endif; ?>
                <div class="col-lg-2 col-md-4 col-sm-6">
                    <div class="form-group mb-2">
                        <label for="date_range" class="form-label">Date</label>
                        <select class="form-select" id="date_range" name="date_range">
                            <option value="">All</option>
                            <option value="today" <?php echo isset($_GET['date_range']) && $_GET['date_range'] === 'today' ? 'selected' : ''; ?>>Today</option>
                            <option value="week" <?php echo isset($_GET['date_range']) && $_GET['date_range'] === 'week' ? 'selected' : ''; ?>>This Week</option>
                            <option value="month" <?php echo isset($_GET['date_range']) && $_GET['date_range'] === 'month' ? 'selected' : ''; ?>>This Month</option>
                            <option value="year" <?php echo isset($_GET['date_range']) && $_GET['date_range'] === 'year' ? 'selected' : ''; ?>>This Year</option>
                        </select>
                    </div>
                </div>
                <div class="col-lg-2 col-md-4 col-sm-6">
                    <div class="form-group mb-2">
                        <label for="sort" class="form-label">Sort</label>
                        <div class="input-group">
                            <select class="form-select border-end-0" id="sort" name="sort">
                                <option value="created_at" <?php echo $sort === 'created_at' ? 'selected' : ''; ?>>Date</option>
                                <option value="name" <?php echo $sort === 'name' ? 'selected' : ''; ?>>Name</option>
                                <option value="username" <?php echo $sort === 'username' ? 'selected' : ''; ?>>Username</option>
                                <option value="phone_number" <?php echo $sort === 'phone_number' ? 'selected' : ''; ?>>Phone</option>
                                <option value="last_login" <?php echo $sort === 'last_login' ? 'selected' : ''; ?>>Last Login</option>
                            </select>
                            <select class="form-select border-start-0 ps-0 bg-light" id="order" name="order" style="max-width: 60px;">
                                <option value="ASC" <?php echo $order === 'ASC' ? 'selected' : ''; ?>><i class="fas fa-sort-up"></i></option>
                                <option value="DESC" <?php echo $order === 'DESC' ? 'selected' : ''; ?>><i class="fas fa-sort-down"></i></option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-12 d-flex justify-content-end mt-2">
                    <a href="users.php" class="btn btn-success btn-sm dashboard-green-btn me-2">
                        <i class="fas fa-redo me-1"></i> Reset
                    </a>
                    <button type="submit" class="btn btn-success btn-sm dashboard-green-btn">
                        <i class="fas fa-filter me-1"></i> Apply
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Users Table -->
<form action="users.php" method="post" id="usersForm">
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
            <div>
                <h6 class="m-0 font-weight-bold text-primary">App Users List</h6>
                <span class="badge bg-primary rounded-pill mt-1"><?php echo number_format($totalUsers); ?> users</span>
            </div>
            <?php if ($isAdmin): ?>
            <div class="d-flex align-items-center">
                <div class="action-menu-container">
                    <button class="btn btn-sm btn-success btn-rounded action-btn" type="button" id="bulkActionDropdown" aria-label="Bulk Actions" title="Bulk Actions">
                        <i class="fas fa-cog me-1"></i> Bulk Actions
                    </button>
                    <div class="action-menu" style="min-width: 220px; max-width: 280px;">
                        <div class="action-menu-header">Select Action</div>
                        <button type="button" class="action-menu-item" onclick="setBulkAction('activate')"><i class="fas fa-check-circle"></i> Activate Selected</button>
                        <button type="button" class="action-menu-item" onclick="setBulkAction('deactivate')"><i class="fas fa-times-circle"></i> Deactivate Selected</button>
                        <?php if ($isSuperAdmin && !empty($staffMembers)): ?>
                        <button type="button" class="action-menu-item" onclick="showStaffAssignModal()"><i class="fas fa-user-tag"></i> Assign to Staff</button>
                        <?php endif; ?>
                        <div class="action-menu-divider"></div>
                        <button type="button" class="action-menu-item danger" onclick="setBulkAction('delete')"><i class="fas fa-trash-alt"></i> Delete Selected</button>
                    </div>
                </div>
                <input type="hidden" name="bulk_action" id="bulkActionInput" value="">
            </div>
            <?php endif; ?>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0">
                    <thead class="bg-light">
                        <tr>
                            <th width="40" class="ps-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="selectAll">
                                </div>
                            </th>
                            <th>User</th>
                            <th>Phone Number</th>
                            <th>Registered</th>
                            <th>Last Login</th>
                            <th class="text-end pe-3">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (!empty($users)): ?>
                            <?php foreach ($users as $user):
                                // Calculate BMI if height and weight are available
                                $bmi = null;
                                if (!empty($user['height']) && !empty($user['weight'])) {
                                    $heightInMeters = $user['height'] / 100;
                                    $bmi = number_format($user['weight'] / ($heightInMeters * $heightInMeters), 1);
                                }
                            ?>
                                <tr>
                                    <td class="ps-3">
                                        <div class="form-check">
                                            <input class="form-check-input user-checkbox" type="checkbox" name="user_ids[]" value="<?php echo $user['id']; ?>" <?php echo $user['id'] == 1 ? 'disabled' : ''; ?>>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <?php
                                              $initials = strtoupper(substr($user['name'] ?? 'U', 0, 1));
                                              $bgColor = '#28a745';
                                              $textColor = '#ffffff';
                                              $avatarSize = '38px';
                                            ?>
                                            <div class="user-avatar-initials"
                                                 style="width:<?= $avatarSize ?>;
                                                        height:<?= $avatarSize ?>;
                                                        background-color:<?= $bgColor ?>;
                                                        color:<?= $textColor ?>;
                                                        border-radius:50%;
                                                        display:inline-flex;
                                                        align-items:center;
                                                        justify-content:center;
                                                        margin-right:12px;
                                                        font-weight:600;
                                                        font-size:15px;
                                                        border:1px solid #218838;
                                                        box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                                                <?= htmlspecialchars($initials) ?>
                                            </div>
                                            <div class="d-flex flex-column">
                                              <div class="fw-medium text-dark"><?php echo htmlspecialchars($user['name']); ?></div>
                                              <div class="d-flex align-items-center">
                                                <small class="text-muted me-2">@<?php echo htmlspecialchars($user['username']); ?></small>
                                                <?php if ($user['id'] == 1): ?>
                                                  <span class="badge bg-primary rounded-pill">Admin</span>
                                                <?php endif; ?>
                                                <?php if (!empty($user['email_verified_at'])): ?>
                                                  <span class="badge bg-success rounded-pill ms-1">Verified</span>
                                                <?php endif; ?>
                                              </div>
                                              <?php if ($isSuperAdmin && !empty($user['assigned_staff_id'])): ?>
                                                <?php
                                                // Get staff name
                                                $staffQuery = "SELECT name FROM admin_users WHERE id = ?";
                                                $staffStmt = $conn->prepare($staffQuery);
                                                $staffStmt->bind_param("i", $user['assigned_staff_id']);
                                                $staffStmt->execute();
                                                $staffResult = $staffStmt->get_result();
                                                if ($staffResult && $staffResult->num_rows > 0) {
                                                    $staffName = $staffResult->fetch_assoc()['name'];
                                                    echo '<div class="small" style="color:#27ae60;"><i class="fas fa-user-tag me-1"></i> ' . htmlspecialchars($staffName) . '</div>';
                                                }
                                                ?>
                                              <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <?php
                                            // Show phone number as a single string, remove + and spaces if present
                                            $rawPhone = $user['phone_number'] ?? 'N/A';
                                            $displayPhone = preg_replace('/[^0-9]/', '', $rawPhone);
                                            echo $displayPhone ?: 'N/A';
                                        ?>
                                    </td>
                                    <td>
                                        <div class="d-flex flex-column">
                                            <div class="small text-muted mb-1">Registered</div>
                                            <div class="d-flex align-items-center">
                                                <i class="far fa-calendar-alt text-muted me-2"></i>
                                                <?php
                                                if (!empty($user['created_at'])) {
                                                    $createdAt = strtotime($user['created_at']);
                                                    $now = time();
                                                    $daysAgo = floor(($now - $createdAt) / 86400);
                                                    echo $daysAgo === 0 ? 'Today' : ($daysAgo === 1 ? '1 day ago' : $daysAgo . ' days ago');
                                                } else {
                                                    echo '';
                                                }
                                                ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex flex-column">
                                            <div class="small text-muted mb-1">Last Login</div>
                                            <?php if ($user['last_login']): ?>
                                                <div class="d-flex align-items-center">
                                                    <i class="far fa-clock text-muted me-2"></i>
                                                    <?php echo !empty($user['last_login']) ? date('M d, Y', strtotime($user['last_login'])) : 'Never'; ?>
                                                </div>
                                                <div class="small text-muted"><?php echo !empty($user['last_login']) ? date('h:i A', strtotime($user['last_login'])) : ''; ?></div>
                                            <?php else: ?>
                                                <span class="text-muted">Never</span>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td class="text-end pe-3" style="position: static !important;">
                                        <div class="d-flex justify-content-end">
                                            <a href="user_view.php?id=<?php echo $user['id']; ?>" class="btn btn-sm btn-icon btn-success btn-rounded me-1 dashboard-green-btn" data-bs-toggle="tooltip" title="View Profile">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="user_edit.php?id=<?php echo $user['id']; ?>" class="btn btn-sm btn-icon btn-success btn-rounded me-1 dashboard-green-btn" data-bs-toggle="tooltip" title="Edit User">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <div class="action-menu-container">
                                                <button class="action-btn" type="button" aria-label="User Actions" title="User Actions">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <div class="action-menu" style="min-width: 200px; max-width: 260px;">
                                                    <div class="action-menu-header">User Actions</div>
                                                    <a class="action-menu-item view-analytics" href="#" data-user-id="<?php echo $user['id']; ?>" data-user-name="<?php echo htmlspecialchars($user['name']); ?>">
                                                        <i class="fas fa-chart-bar"></i> View Analytics
                                                    </a>
                                                    <a class="action-menu-item" href="user_verification.php?id=<?php echo $user['id']; ?>">
                                                        <i class="fas fa-mobile-alt"></i> Verification
                                                    </a>
                                                    <?php if ($user['id'] != 1): ?>
                                                        <div class="action-menu-divider"></div>
                                                        <a class="action-menu-item danger" href="users.php?delete=<?php echo $user['id']; ?>" onclick="return confirm('Are you sure you want to delete this user? This action cannot be undone.')">
                                                            <i class="fas fa-trash-alt"></i> Delete
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="8" class="text-center py-5">
                                    <div class="empty-state">
                                        <div class="empty-state-icon">
                                            <i class="fas fa-users"></i>
                                        </div>
                                        <h5 class="mt-4">No App Users Found</h5>
                                        <p class="text-muted mb-4">No app users match your current search criteria</p>
                                        <a href="users.php" class="btn btn-success dashboard-green-btn">
                                            <i class="fas fa-redo me-2"></i> Clear All Filters
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
        <?php if ($totalPages > 1): ?>
        <div class="card-footer bg-white py-3">
            <div class="d-flex justify-content-between align-items-center">
                <div class="text-muted small">
                    Showing <span class="fw-medium"><?php echo min(($page - 1) * $limit + 1, $totalUsers); ?></span> to
                    <span class="fw-medium"><?php echo min($page * $limit, $totalUsers); ?></span> of
                    <span class="fw-medium"><?php echo $totalUsers; ?></span> users
                </div>
                <nav aria-label="Page navigation">
                    <ul class="pagination pagination-sm mb-0">
                        <li class="page-item <?php echo ($page <= 1) ? 'disabled' : ''; ?>">
                            <a class="page-link" href="<?php echo sprintf($paginationUrl, $page - 1); ?>" aria-label="Previous">
                                <i class="fas fa-chevron-left small"></i>
                            </a>
                        </li>

                        <?php
                        // Show limited page numbers with ellipsis
                        $startPage = max(1, $page - 2);
                        $endPage = min($totalPages, $page + 2);

                        // Build the base URL for pagination
                        $paginationUrl = "?page=%d&search=" . urlencode($search) . "&status=" . urlencode($status) .
                                        "&verification=" . urlencode($verification ?? '') . "&date_range=" . urlencode($dateRange) .
                                        "&sort=" . urlencode($sort) . "&order=" . urlencode($order);

                        // Add staff_id parameter if it exists
                        if (!empty($staffId)) {
                            $paginationUrl .= "&staff_id=" . urlencode($staffId);
                        }

                        // Always show first page
                        if ($startPage > 1) {
                            echo '<li class="page-item"><a class="page-link" href="' . sprintf($paginationUrl, 1) . '">1</a></li>';
                            if ($startPage > 2) {
                                echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
                            }
                        }

                        // Show page numbers
                        for ($i = $startPage; $i <= $endPage; $i++) {
                            echo '<li class="page-item ' . ($page == $i ? 'active' : '') . '"><a class="page-link" href="' . sprintf($paginationUrl, $i) . '">' . $i . '</a></li>';
                        }

                        // Always show last page
                        if ($endPage < $totalPages) {
                            if ($endPage < $totalPages - 1) {
                                echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
                            }
                            echo '<li class="page-item"><a class="page-link" href="' . sprintf($paginationUrl, $totalPages) . '">' . $totalPages . '</a></li>';
                        }
                        ?>

                        <li class="page-item <?php echo ($page >= $totalPages) ? 'disabled' : ''; ?>">
                            <a class="page-link" href="<?php echo sprintf($paginationUrl, $page + 1); ?>" aria-label="Next">
                                <i class="fas fa-chevron-right small"></i>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
        <?php endif; ?>
    </div>
</form>

<!-- JavaScript for User Management -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl, {
            delay: { show: 500, hide: 100 }
        });
    });

    // Select All Checkbox
    const selectAllCheckbox = document.getElementById('selectAll');
    const userCheckboxes = document.querySelectorAll('.user-checkbox:not([disabled])');

    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const isChecked = this.checked;
            userCheckboxes.forEach(checkbox => {
                checkbox.checked = isChecked;
            });
        });
    }

    // Update "Select All" checkbox state based on individual checkboxes
    userCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const allChecked = Array.from(userCheckboxes).every(cb => cb.checked);
            const someChecked = Array.from(userCheckboxes).some(cb => cb.checked);

            if (selectAllCheckbox) {
                selectAllCheckbox.checked = allChecked;
                selectAllCheckbox.indeterminate = someChecked && !allChecked;
            }
        });
    });

    // Handle export button click
    const exportUsersBtn = document.getElementById('exportUsersBtn');
    if (exportUsersBtn) {
        exportUsersBtn.addEventListener('click', function() {
            // Get current URL parameters
            const urlParams = new URLSearchParams(window.location.search);
            const search = urlParams.get('search') || '';
            const status = urlParams.get('status') || '';
            const verification = urlParams.get('verification') || '';
            const dateRange = urlParams.get('date_range') || '';
            const sort = urlParams.get('sort') || 'created_at';
            const order = urlParams.get('order') || 'DESC';

            // Build export URL with current filters
            const exportUrl = `export_users.php?search=${encodeURIComponent(search)}&status=${encodeURIComponent(status)}&verification=${encodeURIComponent(verification)}&date_range=${encodeURIComponent(dateRange)}&sort=${encodeURIComponent(sort)}&order=${encodeURIComponent(order)}`;

            // Open export URL in a new tab
            window.open(exportUrl, '_blank');
        });
    }

    // Bulk action handler
    window.setBulkAction = function(action) {
        const selectedUsers = document.querySelectorAll('.user-checkbox:checked');
        if (selectedUsers.length === 0) {
            alert('Please select at least one user to perform this action.');
            return;
        }

        let confirmMessage = 'Are you sure you want to perform this action on the selected users?';
        if (action === 'delete') {
            confirmMessage = 'WARNING: You are about to delete the selected users. This action cannot be undone. Continue?';
        }

        if (confirm(confirmMessage)) {
            document.getElementById('bulkActionInput').value = action;
            document.getElementById('usersForm').submit();
        }
    };

    // Staff assignment modal handler
    window.showStaffAssignModal = function() {
        const selectedUsers = document.querySelectorAll('.user-checkbox:checked');
        if (selectedUsers.length === 0) {
            alert('Please select at least one user to assign to a staff member.');
            return;
        }

        // Clear previous selections
        const container = document.getElementById('selectedUsersContainer');
        container.innerHTML = '';

        // Add selected users to the form
        selectedUsers.forEach(checkbox => {
            const userId = checkbox.value;
            const userRow = checkbox.closest('tr');
            const userName = userRow.querySelector('.fw-medium').textContent;

            // Create hidden input for each selected user
            const hiddenInput = document.createElement('input');
            hiddenInput.type = 'hidden';
            hiddenInput.name = 'user_ids[]';
            hiddenInput.value = userId;
            container.appendChild(hiddenInput);

            // Create a badge to show selected users
            const badge = document.createElement('span');
            badge.className = 'badge bg-light text-dark me-1 mb-1';
            badge.textContent = userName;
            container.appendChild(badge);
        });

        // Add a summary text
        const summaryText = document.createElement('p');
        summaryText.className = 'mt-2 mb-3';
        summaryText.innerHTML = `<i class="fas fa-info-circle text-primary me-2"></i> You are about to assign <strong>${selectedUsers.length}</strong> selected users to a staff member.`;
        container.insertBefore(summaryText, container.firstChild);

        // Show the modal
        const staffAssignModal = new bootstrap.Modal(document.getElementById('staffAssignModal'));
        staffAssignModal.show();
    };

    // Handle staff assignment confirmation
    document.getElementById('confirmStaffAssign').addEventListener('click', function() {
        const staffId = document.getElementById('assigned_staff_id').value;
        if (!staffId) {
            alert('Please select a staff member.');
            return;
        }

        document.getElementById('staffAssignForm').submit();
    });
});
</script>

<style>
/* Icon Circle for Stats */
.icon-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

/* User Avatar */
.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.4rem 0.8rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.2s ease;
}

.status-badge.active {
    background-color: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.status-badge.inactive {
    background-color: rgba(108, 117, 125, 0.1);
    color: #6c757d;
}

.status-badge:hover {
    opacity: 0.8;
}

/* BMI Badge */
.bmi-badge {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: white;
    font-size: 0.9rem;
}

.bmi-underweight {
    background-color: #36b9cc;
}

.bmi-normal {
    background-color: #1cc88a;
}

.bmi-overweight {
    background-color: #f6c23e;
}

.bmi-obese {
    background-color: #e74a3b;
}

/* Action Buttons */
.btn-icon {
    width: 32px;
    height: 32px;
    padding: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s;
}

.btn-icon:hover {
    background-color: #f8f9fa;
}

/* Empty State */
.empty-state {
    padding: 2rem 1rem;
    text-align: center;
}

.empty-state-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto;
    border-radius: 50%;
    background-color: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: #6c757d;
}

/* Table Styles */
.table td, .table th {
    padding: 0.4rem 0.5rem;
    font-size: 0.95rem;
}

@media (max-width: 600px) {
    .table-responsive {
        font-size: 0.85rem;
    }
    .user-avatar {
        width: 24px;
        height: 24px;
        font-size: 0.8rem;
    }
}

.dashboard-green-btn, .btn-success {
    background-color: #27ae60 !important;
    color: #fff !important;
    border: none !important;
    box-shadow: none !important;
}
.dashboard-green-btn:hover, .btn-success:hover {
    background-color: #219150 !important;
    color: #fff !important;
}
</style>

<!-- Staff Assignment Modal -->
<div class="modal fade" id="staffAssignModal" tabindex="-1" aria-labelledby="staffAssignModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="staffAssignModalLabel">Assign Users to Staff</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="staffAssignForm" action="assign_staff.php" method="post">
                    <div id="selectedUsersContainer"></div>

                    <div class="mb-3">
                        <label for="assigned_staff_id" class="form-label">Select Staff Member</label>
                        <select class="form-select" id="assigned_staff_id" name="assigned_staff_id" required>
                            <option value="">Select Staff Member</option>
                            <?php foreach ($staffMembers as $staff): ?>
                                <option value="<?php echo $staff['id']; ?>">
                                    <?php echo htmlspecialchars($staff['name'] . ' (' . $staff['username'] . ')'); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmStaffAssign">Assign Users</button>
            </div>
        </div>
    </div>
</div>

<!-- User Analytics Modal -->
<div class="modal fade" id="userAnalyticsModal" tabindex="-1" aria-labelledby="userAnalyticsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="userAnalyticsModalLabel">User Analytics</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center py-5" id="analyticsLoader">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading analytics data...</p>
                </div>

                <div id="analyticsContent" style="display: none;">
                    <!-- Analytics Tabs -->
                    <ul class="nav nav-tabs" id="analyticsTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab" aria-controls="overview" aria-selected="true">Overview</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="workouts-tab" data-bs-toggle="tab" data-bs-target="#workouts" type="button" role="tab" aria-controls="workouts" aria-selected="false">Workouts</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="courses-tab" data-bs-toggle="tab" data-bs-target="#courses" type="button" role="tab" aria-controls="courses" aria-selected="false">Courses</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="bmi-tab" data-bs-toggle="tab" data-bs-target="#bmi" type="button" role="tab" aria-controls="bmi" aria-selected="false">BMI Trend</button>
                        </li>
                    </ul>

                    <!-- Tab Content -->
                    <div class="tab-content p-3" id="analyticsTabContent">
                        <!-- Overview Tab -->
                        <div class="tab-pane fade show active" id="overview" role="tabpanel" aria-labelledby="overview-tab">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card mb-3">
                                        <div class="card-body">
                                            <h6 class="card-title">User Summary</h6>
                                            <div id="userSummary"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card mb-3">
                                        <div class="card-body">
                                            <h6 class="card-title">Activity Stats</h6>
                                            <div id="activityStats"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="card">
                                        <div class="card-body">
                                            <h6 class="card-title">Recent Activity</h6>
                                            <div id="recentActivity"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Workouts Tab -->
                        <div class="tab-pane fade" id="workouts" role="tabpanel" aria-labelledby="workouts-tab">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">Workout History</h6>
                                    <div class="chart-container" style="height: 300px;">
                                        <canvas id="workoutChart"></canvas>
                                    </div>
                                    <div class="mt-4" id="workoutList"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Courses Tab -->
                        <div class="tab-pane fade" id="courses" role="tabpanel" aria-labelledby="courses-tab">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">Course Progress</h6>
                                    <div id="courseProgress"></div>
                                </div>
                            </div>
                        </div>

                        <!-- BMI Tab -->
                        <div class="tab-pane fade" id="bmi" role="tabpanel" aria-labelledby="bmi-tab">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">BMI Trend</h6>
                                    <div class="chart-container" style="height: 300px;">
                                        <canvas id="bmiChart"></canvas>
                                    </div>
                                    <div class="mt-4" id="bmiHistory"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <a href="#" id="viewProfileBtn" class="btn btn-primary">View Full Profile</a>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- User Analytics JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle analytics view button click
    const analyticsButtons = document.querySelectorAll('.view-analytics');
    const analyticsModal = new bootstrap.Modal(document.getElementById('userAnalyticsModal'));
    const modalTitle = document.getElementById('userAnalyticsModalLabel');
    const analyticsLoader = document.getElementById('analyticsLoader');
    const analyticsContent = document.getElementById('analyticsContent');
    const viewProfileBtn = document.getElementById('viewProfileBtn');

    let workoutChart = null;
    let bmiChart = null;

    analyticsButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const userId = this.getAttribute('data-user-id');
            const userName = this.getAttribute('data-user-name');

            // Update modal title and profile link
            modalTitle.textContent = `Analytics for ${userName}`;
            viewProfileBtn.href = `user_view.php?id=${userId}`;

            // Show loader, hide content
            analyticsLoader.style.display = 'block';
            analyticsContent.style.display = 'none';

            // Show modal
            analyticsModal.show();

            // Fetch user analytics data
            fetchUserAnalytics(userId);
        });
    });

    function fetchUserAnalytics(userId) {
        // Simulate API call with setTimeout (replace with actual AJAX call)
        setTimeout(() => {
            // Hide loader, show content
            analyticsLoader.style.display = 'none';
            analyticsContent.style.display = 'block';

            // Load user data (replace with actual data from API)
            loadUserData(userId);
        }, 1000);
    }

    function loadUserData(userId) {
        // Fetch user data via AJAX
        fetch(`user_analytics_api.php?user_id=${userId}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // Check if data and required properties exist
                if (!data || !data.user) {
                    throw new Error('Invalid response data: user data is missing');
                }

                // Populate user summary
                populateUserSummary(data.user);

                // Populate activity stats if available
                if (data.stats) {
                    populateActivityStats(data.stats);
                }

                // Populate recent activity if available
                if (data.recent_activity) {
                    populateRecentActivity(data.recent_activity);
                }

                // Populate workout history if available
                if (data.workouts) {
                    populateWorkoutHistory(data.workouts);
                }

                // Populate course progress if available
                if (data.courses) {
                    populateCourseProgress(data.courses);
                }

                // Populate BMI history if available
                if (data.bmi_records) {
                    populateBMIHistory(data.bmi_records);
                }
            })
            .catch(error => {
                console.error('Error fetching user analytics:', error);
                // Show error message in all sections
                const errorHtml = '<div class="alert alert-danger">Failed to load user data. Please try again.</div>';
                document.getElementById('userSummary').innerHTML = errorHtml;
                document.getElementById('activityStats').innerHTML = '';
                document.getElementById('recentActivity').innerHTML = '';
                document.getElementById('workoutList').innerHTML = '';
                document.getElementById('courseProgress').innerHTML = '';
                document.getElementById('bmiHistory').innerHTML = '';

                // Hide charts
                if (document.getElementById('workoutChart')) {
                    document.getElementById('workoutChart').style.display = 'none';
                }
                if (document.getElementById('bmiChart')) {
                    document.getElementById('bmiChart').style.display = 'none';
                }
            });
    }

    function populateUserSummary(user) {
        // Ensure user object has all required properties with defaults
        const userData = {
            name: user.name || 'Unknown',
            username: user.username || 'Unknown',
            phone_number: user.phone_number || 'N/A',
            created_at: user.created_at || new Date().toISOString(),
            is_active: !!user.is_active,
            is_premium: !!user.is_premium
        };

        const summaryHtml = `
            <div class="row">
                <div class="col-6 mb-2">
                    <strong>Name:</strong>
                </div>
                <div class="col-6 mb-2">
                    ${userData.name}
                </div>
                <div class="col-6 mb-2">
                    <strong>Username:</strong>
                </div>
                <div class="col-6 mb-2">
                    ${userData.username}
                </div>
                <div class="col-6 mb-2">
                    <strong>Phone:</strong>
                </div>
                <div class="col-6 mb-2">
                    ${userData.phone_number}
                </div>
                <div class="col-6 mb-2">
                    <strong>Member Since:</strong>
                </div>
                <div class="col-6 mb-2">
                    ${new Date(userData.created_at).toLocaleDateString()}
                </div>
                <div class="col-6 mb-2">
                    <strong>Status:</strong>
                </div>
                <div class="col-6 mb-2">
                    ${userData.is_active ? '<span class="badge bg-success">Active</span>' : '<span class="badge bg-secondary">Inactive</span>'}
                    ${userData.is_premium ? '<span class="badge bg-warning ms-1">Premium</span>' : ''}
                </div>
            </div>
        `;

        document.getElementById('userSummary').innerHTML = summaryHtml;
    }

    function populateActivityStats(stats) {
        const statsHtml = `
            <div class="row text-center">
                <div class="col-4">
                    <div class="h3 mb-0">${stats.total_workouts || 0}</div>
                    <div class="small text-muted">Workouts</div>
                </div>
                <div class="col-4">
                    <div class="h3 mb-0">${stats.total_courses || 0}</div>
                    <div class="small text-muted">Courses</div>
                </div>
                <div class="col-4">
                    <div class="h3 mb-0">${stats.streak_days || 0}</div>
                    <div class="small text-muted">Day Streak</div>
                </div>
                <div class="col-6 mt-3">
                    <div class="h4 mb-0">${stats.total_workout_minutes || 0}</div>
                    <div class="small text-muted">Workout Minutes</div>
                </div>
                <div class="col-6 mt-3">
                    <div class="h4 mb-0">${stats.current_bmi || 'N/A'}</div>
                    <div class="small text-muted">Current BMI</div>
                </div>
            </div>
        `;

        document.getElementById('activityStats').innerHTML = statsHtml;
    }

    function populateRecentActivity(activities) {
        if (!activities || activities.length === 0) {
            document.getElementById('recentActivity').innerHTML = '<p class="text-muted text-center py-3">No recent activity found.</p>';
            return;
        }

        let activityHtml = '<div class="list-group">';

        activities.forEach(activity => {
            let icon, badgeClass, activityText;

            switch (activity.type) {
                case 'workout':
                    icon = 'fas fa-dumbbell';
                    badgeClass = 'bg-success';
                    const duration = activity.duration_minutes || 0;
                    const title = activity.title || 'Workout';
                    activityText = `Completed a ${duration} minute workout: ${title}`;
                    break;
                case 'course_progress':
                    icon = 'fas fa-video';
                    badgeClass = 'bg-primary';
                    activityText = `Watched a video in course: ${activity.course_title}`;
                    break;
                case 'bmi_update':
                    icon = 'fas fa-weight';
                    badgeClass = 'bg-info';
                    activityText = `Updated BMI to ${activity.bmi} (Weight: ${activity.weight} kg)`;
                    break;
                default:
                    icon = 'fas fa-check-circle';
                    badgeClass = 'bg-secondary';
                    activityText = activity.description;
            }

            activityHtml += `
                <div class="list-group-item list-group-item-action">
                    <div class="d-flex w-100 justify-content-between">
                        <div>
                            <span class="badge ${badgeClass} me-2"><i class="${icon}"></i></span>
                            ${activityText}
                        </div>
                        <small class="text-muted">${new Date(activity.timestamp).toLocaleString()}</small>
                    </div>
                </div>
            `;
        });

        activityHtml += '</div>';
        document.getElementById('recentActivity').innerHTML = activityHtml;
    }

    function populateWorkoutHistory(workouts) {
        if (!workouts || workouts.length === 0) {
            document.getElementById('workoutList').innerHTML = '<p class="text-muted text-center py-3">No workout history found.</p>';
            document.getElementById('workoutChart').style.display = 'none';
            return;
        }

        // Prepare data for chart
        const dates = [];
        const minutes = [];

        // Sort workouts by date
        workouts.sort((a, b) => new Date(a.recorded_at) - new Date(b.recorded_at));

        // Get the last 10 workouts for the chart
        const recentWorkouts = workouts.slice(-10);

        recentWorkouts.forEach(workout => {
            dates.push(new Date(workout.recorded_at).toLocaleDateString());
            minutes.push(workout.duration_minutes);
        });

        // Create or update chart
        const ctx = document.getElementById('workoutChart').getContext('2d');

        if (workoutChart) {
            workoutChart.destroy();
        }

        workoutChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: dates,
                datasets: [{
                    label: 'Workout Duration (minutes)',
                    data: minutes,
                    backgroundColor: '#4361ee',
                    borderColor: '#3a56d4',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Minutes'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Date'
                        }
                    }
                }
            }
        });

        // Create workout list
        let workoutListHtml = '<div class="table-responsive"><table class="table table-sm table-hover">';
        workoutListHtml += '<thead><tr><th>Date</th><th>Workout</th><th>Duration</th></tr></thead><tbody>';

        workouts.slice(-5).reverse().forEach(workout => {
            const title = workout.title || 'Workout';
            const duration = workout.duration || workout.duration_minutes || 0;

            workoutListHtml += `
                <tr>
                    <td>${new Date(workout.recorded_at).toLocaleDateString()}</td>
                    <td>${title}</td>
                    <td>${duration} minutes</td>
                </tr>
            `;
        });

        workoutListHtml += '</tbody></table></div>';

        if (workouts.length > 5) {
            workoutListHtml += `<div class="text-center"><small class="text-muted">Showing 5 of ${workouts.length} workouts</small></div>`;
        }

        document.getElementById('workoutList').innerHTML = workoutListHtml;
    }

    function populateCourseProgress(courses) {
        if (!courses || courses.length === 0) {
            document.getElementById('courseProgress').innerHTML = '<p class="text-muted text-center py-3">Not enrolled in any courses.</p>';
            return;
        }

        let coursesHtml = '';

        courses.forEach(course => {
            const progressPercentage = course.progress_percentage || 0;
            const progressBarClass = progressPercentage < 25 ? 'bg-danger' :
                                    progressPercentage < 50 ? 'bg-warning' :
                                    progressPercentage < 75 ? 'bg-info' : 'bg-success';

            coursesHtml += `
                <div class="mb-4">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <h6 class="mb-0">${course.title}</h6>
                        <span class="badge bg-secondary">${course.status}</span>
                    </div>
                    <div class="progress mb-2" style="height: 10px;">
                        <div class="progress-bar ${progressBarClass}" role="progressbar" style="width: ${progressPercentage}%"
                             aria-valuenow="${progressPercentage}" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                    <div class="d-flex justify-content-between">
                        <small class="text-muted">
                            <span class="fw-bold">${course.completed_videos}</span> of ${course.total_videos} videos completed
                        </small>
                        <small class="text-muted">${progressPercentage}% complete</small>
                    </div>
                    <div class="mt-2 small text-muted">
                        <span class="me-3">Started: ${new Date(course.start_date).toLocaleDateString()}</span>
                        <span>Ends: ${new Date(course.end_date).toLocaleDateString()}</span>
                    </div>
                </div>
            `;
        });

        document.getElementById('courseProgress').innerHTML = coursesHtml;
    }

    function populateBMIHistory(bmiRecords) {
        if (!bmiRecords || bmiRecords.length === 0) {
            document.getElementById('bmiHistory').innerHTML = '<p class="text-muted text-center py-3">No BMI records found.</p>';
            document.getElementById('bmiChart').style.display = 'none';
            return;
        }

        // Prepare data for chart
        const dates = [];
        const bmiValues = [];
        const weights = [];

        // Sort BMI records by date
        bmiRecords.sort((a, b) => new Date(a.recorded_at) - new Date(b.recorded_at));

        bmiRecords.forEach(record => {
            dates.push(new Date(record.recorded_at).toLocaleDateString());
            bmiValues.push(record.bmi);
            weights.push(record.weight);
        });

        // Create or update chart
        const ctx = document.getElementById('bmiChart').getContext('2d');

        if (bmiChart) {
            bmiChart.destroy();
        }

        bmiChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: dates,
                datasets: [
                    {
                        label: 'BMI',
                        data: bmiValues,
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        borderColor: 'rgba(75, 192, 192, 1)',
                        borderWidth: 2,
                        tension: 0.1,
                        yAxisID: 'y'
                    },
                    {
                        label: 'Weight (kg)',
                        data: weights,
                        backgroundColor: 'rgba(153, 102, 255, 0.2)',
                        borderColor: 'rgba(153, 102, 255, 1)',
                        borderWidth: 2,
                        tension: 0.1,
                        yAxisID: 'y1'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'BMI'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Weight (kg)'
                        },
                        grid: {
                            drawOnChartArea: false
                        }
                    }
                }
            }
        });

        // Create BMI history table
        let bmiHistoryHtml = '<div class="table-responsive"><table class="table table-sm table-hover">';
        bmiHistoryHtml += '<thead><tr><th>Date</th><th>Weight (kg)</th><th>BMI</th><th>Category</th></tr></thead><tbody>';

        bmiRecords.slice(-5).reverse().forEach(record => {
            let category, badgeClass;

            if (record.bmi < 18.5) {
                category = 'Underweight';
                badgeClass = 'bg-info';
            } else if (record.bmi < 25) {
                category = 'Normal';
                badgeClass = 'bg-success';
            } else if (record.bmi < 30) {
                category = 'Overweight';
                badgeClass = 'bg-warning';
            } else {
                category = 'Obese';
                badgeClass = 'bg-danger';
            }

            bmiHistoryHtml += `
                <tr>
                    <td>${new Date(record.recorded_at).toLocaleDateString()}</td>
                    <td>${record.weight} kg</td>
                    <td>${record.bmi}</td>
                    <td><span class="badge ${badgeClass}">${category}</span></td>
                </tr>
            `;
        });

        bmiHistoryHtml += '</tbody></table></div>';

        if (bmiRecords.length > 5) {
            bmiHistoryHtml += `<div class="text-center"><small class="text-muted">Showing 5 of ${bmiRecords.length} records</small></div>`;
        }

        document.getElementById('bmiHistory').innerHTML = bmiHistoryHtml;
    }
});
</script>

<style>
.minimal-export-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: none;
    color: #888;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    font-size: 1.1rem;
    box-shadow: none;
    transition: color 0.18s;
}
.minimal-export-btn:hover, .minimal-export-btn:focus {
    color: #27ae60;
    background: none;
}
</style>

<?php require_once 'includes/footer.php'; ?>