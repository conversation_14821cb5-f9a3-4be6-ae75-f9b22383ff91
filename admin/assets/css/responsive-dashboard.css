/**
 * Comprehensive Responsive Admin Dashboard CSS
 * Mobile-first design with professional premium aesthetics
 * Maintains all existing functionality while enhancing UX
 */

/* ===== CSS VARIABLES ===== */
:root {
  /* Responsive Breakpoints */
  --mobile-max: 767.98px;
  --tablet-min: 768px;
  --tablet-max: 991.98px;
  --desktop-min: 992px;
  --large-desktop-min: 1200px;
  
  /* Sidebar Variables */
  --sidebar-width: 280px;
  --sidebar-collapsed-width: 60px;
  --sidebar-bg: #111;
  --sidebar-text: #fff;
  --sidebar-hover: rgba(255, 255, 255, 0.1);
  --sidebar-active: rgba(39, 174, 96, 0.2);
  --sidebar-border: rgba(255, 255, 255, 0.1);
  
  /* Animation Variables */
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.4s ease;
  --bounce-transition: 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  
  /* Z-index Stack */
  --z-sidebar: 1050;
  --z-bottom-nav: 1040;
  --z-overlay: 1030;
  --z-dropdown: 1020;
  
  /* Bottom Navigation */
  --bottom-nav-height: 70px;
  --bottom-nav-bg: #fff;
  --bottom-nav-border: #e0e0e0;
  --bottom-nav-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  
  /* Premium Colors */
  --primary-color: #111;
  --secondary-color: #666;
  --accent-color: #27ae60;
  --success-color: #28a745;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
  --info-color: #17a2b8;
  
  /* Card & Content */
  --card-bg: #fff;
  --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  --card-hover-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  --border-radius: 12px;
  --border-color: #e0e0e0;
}

/* ===== MOBILE-FIRST BASE STYLES ===== */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: var(--primary-color);
  background-color: #f8f9fa;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

/* ===== ENHANCED SIDEBAR STYLES ===== */
#sidebar-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: var(--sidebar-width);
  height: 100vh;
  background: var(--sidebar-bg);
  color: var(--sidebar-text);
  z-index: var(--z-sidebar);
  transform: translateX(-100%);
  transition: transform var(--transition-normal);
  overflow-y: auto;
  overflow-x: hidden;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

/* Sidebar visible state */
#wrapper.toggled #sidebar-wrapper,
#sidebar-wrapper.show {
  transform: translateX(0);
}

/* Enhanced sidebar heading */
#sidebar-wrapper .sidebar-heading {
  position: relative;
  padding: 1.5rem 1.25rem;
  border-bottom: 1px solid var(--sidebar-border);
  background: rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 80px;
}

#sidebar-wrapper .sidebar-heading h4 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--sidebar-text);
  transition: opacity var(--transition-fast);
}

/* Enhanced close button */
.sidebar-close {
  position: absolute;
  top: 50%;
  right: 1rem;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: var(--sidebar-text);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
  z-index: 10;
}

.sidebar-close:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-50%) rotate(90deg);
}

.sidebar-close:active {
  transform: translateY(-50%) rotate(90deg) scale(0.95);
}

/* Sidebar collapse toggle */
.sidebar-collapse-toggle {
  position: absolute;
  top: 50%;
  right: -18px;
  transform: translateY(-50%);
  background: var(--sidebar-bg);
  border: 2px solid var(--border-color);
  color: var(--sidebar-text);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
  z-index: 10;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.sidebar-collapse-toggle:hover {
  background: var(--accent-color);
  border-color: var(--accent-color);
  transform: translateY(-50%) scale(1.1);
}

/* Collapsed sidebar state */
#sidebar-wrapper.collapsed {
  width: var(--sidebar-collapsed-width);
}

#sidebar-wrapper.collapsed .sidebar-heading h4,
#sidebar-wrapper.collapsed .sidebar-section-header,
#sidebar-wrapper.collapsed .sidebar-menu-item span {
  opacity: 0;
  pointer-events: none;
}

#sidebar-wrapper.collapsed .sidebar-menu-item {
  justify-content: center;
  padding: 0.75rem;
}

#sidebar-wrapper.collapsed .sidebar-menu-item i {
  margin-right: 0;
}

/* Enhanced sidebar menu items */
.sidebar-menu-item {
  display: flex;
  align-items: center;
  padding: 0.875rem 1.5rem;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all var(--transition-fast);
  border-left: 3px solid transparent;
  margin: 0.125rem 0;
  position: relative;
  overflow: hidden;
}

.sidebar-menu-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  background: linear-gradient(90deg, var(--accent-color), transparent);
  transition: width var(--transition-fast);
  z-index: -1;
}

.sidebar-menu-item:hover::before {
  width: 100%;
}

.sidebar-menu-item:hover {
  color: var(--sidebar-text);
  background: var(--sidebar-hover);
  border-left-color: var(--accent-color);
  transform: translateX(4px);
}

.sidebar-menu-item.active {
  color: var(--sidebar-text);
  background: var(--sidebar-active);
  border-left-color: var(--accent-color);
}

.sidebar-menu-item i {
  width: 24px;
  margin-right: 0.875rem;
  font-size: 1.1rem;
  text-align: center;
  transition: transform var(--transition-fast);
}

.sidebar-menu-item:hover i {
  transform: scale(1.1);
}

/* Sidebar sections */
.sidebar-section {
  margin-bottom: 1.5rem;
}

.sidebar-section-header {
  padding: 0.75rem 1.5rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  color: rgba(255, 255, 255, 0.5);
  letter-spacing: 0.5px;
  transition: opacity var(--transition-fast);
}

/* Page content wrapper */
#page-content-wrapper {
  width: 100%;
  min-height: 100vh;
  margin-left: 0;
  transition: all var(--transition-normal);
  position: relative;
  background: #f8f9fa;
}

/* Content padding for mobile bottom nav */
#page-content-wrapper .container-fluid:last-child {
  padding-bottom: calc(var(--bottom-nav-height) + 1rem);
}

/* ===== BOTTOM NAVIGATION BAR ===== */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: var(--bottom-nav-height);
  background: var(--bottom-nav-bg);
  border-top: 1px solid var(--bottom-nav-border);
  box-shadow: var(--bottom-nav-shadow);
  z-index: var(--z-bottom-nav);
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 0 1rem;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.bottom-nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  color: var(--secondary-color);
  transition: all var(--transition-fast);
  padding: 0.5rem;
  border-radius: 8px;
  min-width: 60px;
  position: relative;
  overflow: hidden;
}

.bottom-nav-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: var(--accent-color);
  transition: all var(--transition-fast);
  transform: translateX(-50%);
}

.bottom-nav-item.active::before {
  width: 80%;
}

.bottom-nav-item:hover,
.bottom-nav-item.active {
  color: var(--primary-color);
  background: rgba(39, 174, 96, 0.1);
  transform: translateY(-2px);
}

.bottom-nav-item i {
  font-size: 1.25rem;
  margin-bottom: 0.25rem;
  transition: transform var(--transition-fast);
}

.bottom-nav-item:hover i {
  transform: scale(1.1);
}

.bottom-nav-item span {
  font-size: 0.75rem;
  font-weight: 500;
  text-align: center;
  line-height: 1;
}

/* Sidebar overlay for mobile */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: calc(var(--z-sidebar) - 1);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
  backdrop-filter: blur(2px);
  -webkit-backdrop-filter: blur(2px);
}

.sidebar-overlay.show {
  opacity: 1;
  visibility: visible;
}

/* ===== RESPONSIVE BREAKPOINTS ===== */

/* Mobile First (up to 767px) */
@media (max-width: 767.98px) {
  /* Show bottom navigation */
  .bottom-nav {
    display: flex;
  }

  /* Hide desktop sidebar toggle */
  .navbar-toggler {
    display: block !important;
  }

  /* Sidebar full overlay on mobile */
  #sidebar-wrapper {
    width: min(var(--sidebar-width), 85vw);
  }

  /* Top navbar adjustments */
  .top-navbar {
    padding: 0.75rem 1rem;
  }

  /* Content adjustments */
  .container-fluid {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  /* Dashboard cards stack vertically */
  .dashboard-card {
    margin-bottom: 1rem;
  }

  /* Table responsive improvements */
  .table-responsive {
    border: none;
    box-shadow: none;
  }

  /* Form improvements */
  .form-control,
  .form-select {
    font-size: 16px; /* Prevent zoom on iOS */
  }
}

/* Tablet (768px to 991px) */
@media (min-width: 768px) and (max-width: 991.98px) {
  /* Hide bottom navigation */
  .bottom-nav {
    display: none;
  }

  /* Sidebar behavior */
  #sidebar-wrapper {
    transform: translateX(-100%);
  }

  #wrapper.toggled #sidebar-wrapper {
    transform: translateX(0);
  }

  /* Content adjustments */
  #page-content-wrapper {
    width: 100%;
    margin-left: 0;
  }

  #wrapper.toggled #page-content-wrapper {
    margin-left: var(--sidebar-width);
    width: calc(100% - var(--sidebar-width));
  }

  /* 2-column layout for tablets */
  .row .col-xl-3,
  .row .col-lg-4 {
    flex: 0 0 50%;
    max-width: 50%;
  }

  .row .col-xl-6,
  .row .col-lg-6 {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

/* Desktop (992px and up) */
@media (min-width: 992px) {
  /* Hide bottom navigation */
  .bottom-nav {
    display: none;
  }

  /* Show sidebar by default */
  #sidebar-wrapper {
    transform: translateX(0);
  }

  /* Adjust content for sidebar */
  #page-content-wrapper {
    margin-left: var(--sidebar-width);
    width: calc(100% - var(--sidebar-width));
  }

  /* Collapsed sidebar adjustments */
  #sidebar-wrapper.collapsed + #page-content-wrapper {
    margin-left: var(--sidebar-collapsed-width);
    width: calc(100% - var(--sidebar-collapsed-width));
  }

  /* Multi-column layout for desktop */
  .dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
  }

  /* Enhanced hover effects for desktop */
  .dashboard-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--card-hover-shadow);
  }
}

/* Large Desktop (1200px and up) */
@media (min-width: 1200px) {
  /* Wider content spacing */
  .container-fluid {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  /* Enhanced grid layouts */
  .dashboard-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
  }
}

/* ===== PREMIUM ANIMATIONS & EFFECTS ===== */

/* Smooth page transitions */
.page-transition {
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading states */
.loading {
  position: relative;
  overflow: hidden;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Pulse animation for notifications */
.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* Bounce animation for interactive elements */
.bounce-in {
  animation: bounceIn 0.6s ease;
}

@keyframes bounceIn {
  0% { transform: scale(0.3); opacity: 0; }
  50% { transform: scale(1.05); }
  70% { transform: scale(0.9); }
  100% { transform: scale(1); opacity: 1; }
}

/* Slide animations */
.slide-in-left {
  animation: slideInLeft 0.5s ease;
}

.slide-in-right {
  animation: slideInRight 0.5s ease;
}

@keyframes slideInLeft {
  from { transform: translateX(-100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInRight {
  from { transform: translateX(100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

/* ===== ENHANCED CARD STYLES ===== */
.premium-card {
  background: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.premium-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, var(--accent-color), var(--info-color));
  transform: scaleX(0);
  transition: transform var(--transition-normal);
}

.premium-card:hover::before {
  transform: scaleX(1);
}

.premium-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--card-hover-shadow);
}

/* ===== UTILITY CLASSES ===== */
.text-gradient {
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.glass-effect {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

.shadow-soft {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.shadow-medium {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.shadow-strong {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.16);
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus styles */
.sidebar-menu-item:focus,
.bottom-nav-item:focus,
.sidebar-close:focus,
.sidebar-collapse-toggle:focus {
  outline: 2px solid var(--accent-color);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --sidebar-bg: #000;
    --sidebar-text: #fff;
    --primary-color: #000;
    --secondary-color: #333;
    --border-color: #666;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --card-bg: #1a1a1a;
    --primary-color: #fff;
    --secondary-color: #ccc;
    --border-color: #333;
    --bottom-nav-bg: #1a1a1a;
    --bottom-nav-border: #333;
  }

  body {
    background-color: #0d1117;
    color: var(--primary-color);
  }

  #page-content-wrapper {
    background: #0d1117;
  }
}

/* Print styles */
@media print {
  .sidebar-wrapper,
  .bottom-nav,
  .sidebar-overlay,
  .top-navbar {
    display: none !important;
  }

  #page-content-wrapper {
    margin-left: 0 !important;
    width: 100% !important;
  }

  .premium-card {
    box-shadow: none;
    border: 1px solid #ccc;
  }
}
