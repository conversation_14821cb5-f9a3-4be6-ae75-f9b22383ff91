/**
 * Fixed Sidebar CSS
 * This CSS ensures the sidebar is responsive and works well on all devices
 */

/* Base sidebar styles */
#sidebar-wrapper {
    min-height: 100vh;
    width: 280px;
    flex: 0 0 280px;
    background: #111;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    z-index: 1050;
    position: fixed;
    height: 100%;
    top: 0;
    left: 0;
    overflow-y: auto;
    transition: transform 0.3s ease, width 0.3s ease;
}

/* Page content wrapper */
#page-content-wrapper {
    width: calc(100% - 280px);
    min-height: 100vh;
    margin-left: 280px;
    position: relative;
    flex: 1;
    transition: margin-left 0.3s ease, width 0.3s ease;
}

/* Wrapper styles */
#wrapper {
    display: flex;
    overflow-x: hidden;
    position: relative;
    min-height: 100vh;
    width: 100%;
}

/* Sidebar heading */
#sidebar-wrapper .sidebar-heading {
    padding: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.2);
}

#sidebar-wrapper .sidebar-heading h4 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: white;
}

/* Close button styles */
.sidebar-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    z-index: 1060;
}

.sidebar-close:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: rotate(90deg);
}

.sidebar-close i {
    font-size: 1.25rem;
}

/* Sidebar menu */
.sidebar-menu {
    padding: 1rem 0;
}

.sidebar-menu-item {
    padding: 0.75rem 1.5rem;
    display: flex;
    align-items: center;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
    margin: 0.25rem 0;
}

.sidebar-menu-item:hover {
    color: white;
    background: rgba(255, 255, 255, 0.1);
    border-left-color: #27ae60;
}

.sidebar-menu-item.active {
    color: white;
    background: rgba(39, 174, 96, 0.2);
    border-left-color: #27ae60;
}

.sidebar-menu-item i {
    width: 24px;
    margin-right: 0.75rem;
    font-size: 1rem;
}

/* Sidebar sections */
.sidebar-section {
    margin-bottom: 1rem;
}

.sidebar-section-header {
    padding: 0.75rem 1.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    color: rgba(255, 255, 255, 0.5);
    letter-spacing: 0.5px;
}

/* Responsive styles */
@media (max-width: 991.98px) {
    #sidebar-wrapper {
        transform: translateX(-280px);
    }

    #page-content-wrapper {
        width: 100%;
        margin-left: 0;
    }

    #wrapper.toggled #sidebar-wrapper {
        transform: translateX(0);
    }

    #wrapper.toggled #page-content-wrapper {
        margin-left: 280px;
        width: calc(100% - 280px);
    }
}

@media (max-width: 767.98px) {
    #wrapper.toggled #page-content-wrapper {
        margin-left: 0;
        width: 100%;
    }

    .sidebar-menu-item {
        padding: 0.75rem 1.25rem;
    }

    .sidebar-section-header {
        padding: 0.75rem 1.25rem;
    }
}

/* Scrollbar styles */
#sidebar-wrapper::-webkit-scrollbar {
    width: 6px;
}

#sidebar-wrapper::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

#sidebar-wrapper::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
}

#sidebar-wrapper::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Touch device optimizations */
@media (hover: none) {
    .sidebar-menu-item {
        padding: 1rem 1.5rem;
    }

    .sidebar-menu-item i {
        font-size: 1.25rem;
    }
}
