<?php
require_once 'config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Device-ID, X-Session-ID');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    returnError('Method not allowed', 405);
}

try {
    // Get input data
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        returnError('Invalid JSON input', 400);
    }

    // Get headers
    $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    $deviceId = $_SERVER['HTTP_X_DEVICE_ID'] ?? '';
    $sessionId = $_SERVER['HTTP_X_SESSION_ID'] ?? '';

    // Validate required fields
    if (empty($authHeader)) {
        returnError('Authorization header required', 401);
    }

    if (empty($deviceId)) {
        returnError('Device ID required', 400);
    }

    // Extract token from Authorization header
    if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
        returnError('Invalid authorization header format', 401);
    }
    $token = $matches[1];

    // Validate token and get user data
    $tokenData = validateApiToken($token);
    if (!$tokenData) {
        returnError('Invalid or expired token', 401);
    }

    $userId = $tokenData['user_id'];

    // Get user's current device ID from database
    $stmt = $conn->prepare("SELECT device_id, name FROM users WHERE id = ? AND is_active = 1");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    $user = $result->fetch_assoc();
    $stmt->close();

    if (!$user) {
        returnError('User not found or inactive', 401);
    }

    // Check if device ID matches or if device was revoked by admin
    if ($user['device_id'] !== $deviceId || $user['device_id'] === null) {
        // Check if this was an admin revocation
        $revocationStmt = $conn->prepare("
            SELECT ds.revocation_reason, au.username as revoked_by
            FROM device_sessions ds
            LEFT JOIN admin_users au ON ds.revoked_by_admin = au.id
            WHERE ds.user_id = ? AND ds.device_id = ? AND ds.is_active = 0 AND ds.revoked_by_admin IS NOT NULL
            ORDER BY ds.revoked_at DESC LIMIT 1
        ");
        $revocationStmt->bind_param("is", $userId, $deviceId);
        $revocationStmt->execute();
        $revocationResult = $revocationStmt->get_result();
        $revocationInfo = $revocationResult->fetch_assoc();
        $revocationStmt->close();

        if ($revocationInfo) {
            // Admin revocation detected
            $message = 'Your session has been terminated by an administrator. Please contact support if you believe this is an error.';
            $reason = 'admin_revocation';

            error_log("Session invalidation: User {$userId} ({$user['name']}) - Admin revocation by {$revocationInfo['revoked_by']}. Device: {$deviceId}");
        } else {
            // Regular device mismatch
            $message = 'You have been logged out because your account was accessed from another device. Please log in again to continue.';
            $reason = 'device_mismatch';

            error_log("Session invalidation: User {$userId} ({$user['name']}) - Device mismatch. Expected: {$user['device_id']}, Got: {$deviceId}");
        }

        // Invalidate all tokens for this user and device
        $deleteStmt = $conn->prepare("DELETE FROM api_tokens WHERE user_id = ? AND (device_id = ? OR device_id IS NULL)");
        $deleteStmt->bind_param("is", $userId, $deviceId);
        $deleteStmt->execute();
        $deleteStmt->close();

        returnResponse([
            'success' => false,
            'reason' => $reason,
            'message' => $message,
            'current_device' => $user['device_id'],
            'request_device' => $deviceId,
            'revocation_info' => $revocationInfo
        ], 401);
    }

    // Check token expiry in database
    $tokenStmt = $conn->prepare("SELECT expires_at FROM api_tokens WHERE token = ? AND user_id = ?");
    $tokenStmt->bind_param("si", $token, $userId);
    $tokenStmt->execute();
    $tokenResult = $tokenStmt->get_result();
    $tokenInfo = $tokenResult->fetch_assoc();
    $tokenStmt->close();

    if (!$tokenInfo) {
        returnError('Token not found in database', 401);
    }

    // Check if token is expired
    $expiresAt = strtotime($tokenInfo['expires_at']);
    if ($expiresAt < time()) {
        // Token expired
        $deleteExpiredStmt = $conn->prepare("DELETE FROM api_tokens WHERE token = ?");
        $deleteExpiredStmt->bind_param("s", $token);
        $deleteExpiredStmt->execute();
        $deleteExpiredStmt->close();
        
        returnResponse([
            'success' => false,
            'reason' => 'token_expired',
            'message' => 'Your session has expired. Please log in again.',
            'expired_at' => $tokenInfo['expires_at']
        ], 401);
    }

    // Update last activity timestamp
    $updateStmt = $conn->prepare("UPDATE api_tokens SET last_used = NOW() WHERE token = ?");
    $updateStmt->bind_param("s", $token);
    $updateStmt->execute();
    $updateStmt->close();

    // Update user's last login timestamp
    $updateUserStmt = $conn->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
    $updateUserStmt->bind_param("i", $userId);
    $updateUserStmt->execute();
    $updateUserStmt->close();

    // Session is valid
    returnResponse([
        'success' => true,
        'message' => 'Session is valid',
        'user_id' => $userId,
        'device_id' => $user['device_id'],
        'session_id' => $sessionId,
        'validated_at' => date('Y-m-d H:i:s'),
        'expires_at' => $tokenInfo['expires_at']
    ]);

} catch (Exception $e) {
    error_log("Session validation error: " . $e->getMessage());
    returnError('Internal server error during session validation', 500);
}

/**
 * Validate API token using existing validation logic
 */
function validateApiToken($token) {
    global $conn;
    
    try {
        // First try JWT validation
        require_once '../includes/jwt.php';
        $secret = defined('APP_SECRET') ? APP_SECRET : 'your-secret-key';
        
        $payload = validate_jwt($token, $secret);
        if ($payload && isset($payload['user_id'])) {
            return [
                'user_id' => $payload['user_id'],
                'name' => $payload['name'] ?? '',
                'exp' => $payload['exp'] ?? 0
            ];
        }
    } catch (Exception $e) {
        error_log("JWT validation failed: " . $e->getMessage());
    }
    
    // Fall back to database token validation
    $stmt = $conn->prepare("
        SELECT t.*, u.id as user_id, u.name, u.is_active
        FROM api_tokens t
        JOIN users u ON t.user_id = u.id
        WHERE t.token = ? AND t.expires_at > NOW() AND u.is_active = 1
    ");
    $stmt->bind_param("s", $token);
    $stmt->execute();
    $result = $stmt->get_result();
    $tokenData = $result->fetch_assoc();
    $stmt->close();
    
    return $tokenData ?: null;
}

/**
 * Return JSON response
 */
function returnResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    echo json_encode($data);
    exit;
}

/**
 * Return error response
 */
function returnError($message, $statusCode = 400) {
    returnResponse([
        'success' => false,
        'error' => $message,
        'timestamp' => date('Y-m-d H:i:s')
    ], $statusCode);
}
?>
