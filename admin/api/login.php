<?php
require_once 'config.php';
require_once '../includes/utilities.php';

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    returnError('Method not allowed', 405);
}

// Get request data
$data = getRequestData();

// Log the request data in development mode
if (defined('DEV_MODE') && DEV_MODE === true) {
    error_log('Login request data: ' . print_r($data, true));
}

// Check if this is a verification request or initial login
$isVerification = isset($data['verification_code']) && !empty($data['verification_code']);

// For initial login, we need a phone number
if (!$isVerification && empty($data['phone_number'])) {
    returnError('Phone number is required');
}

// For verification, we need both phone number and verification code
if ($isVerification && (empty($data['phone_number']) || empty($data['verification_code']))) {
    returnError('Phone number and verification code are required');
}

// Require PIN for login
if (!$isVerification && (empty($data['pin']) || !preg_match('/^\d{4}$/', $data['pin']))) {
    returnError('A valid 4-digit PIN is required');
}

// Sanitize inputs
$phoneNumber = sanitizeInput($data['phone_number']);
$verificationCode = $isVerification ? sanitizeInput($data['verification_code']) : null;
$deviceId = isset($data['device_id']) ? sanitizeInput($data['device_id']) : null;
$pin = $isVerification ? null : sanitizeInput($data['pin']);

// Extract last 10 digits for matching
$phoneLast10 = preg_replace('/[^0-9]/', '', $phoneNumber);
$phoneLast10 = substr($phoneLast10, -10);

// Connect to database
$conn = getDbConnection();

// Get all users and check in PHP (MySQL REGEXP_REPLACE is not available)
$query = "SELECT id, username, name, email, phone_number, device_id, is_active FROM users";
$result = $conn->query($query);
$user = null;
while ($row = $result->fetch_assoc()) {
    $digits = preg_replace('/[^0-9]/', '', $row['phone_number']);
    if (substr($digits, -10) === $phoneLast10) {
        $user = $row;
        break;
    }
}
if (!$user) {
    // Not registered
    returnResponse([
        'success' => false,
        'error' => 'not_registered',
        'message' => 'This number is not registered. Please fill the registration form or contact admin.'
    ]);
}

// Check if user is active
if (!$user['is_active']) {
    returnResponse([
        'success' => false,
        'error' => 'inactive',
        'message' => 'Your account is inactive. Please contact admin.'
    ]);
}

// After finding the user by phone, check PIN and expiry
if (!$isVerification && $user) {
    error_log('DEBUG: Login attempt for user_id=' . $user['id'] . ', phone=' . $phoneNumber . ', entered PIN=' . $pin);
    $stmt = $conn->prepare('SELECT pin, pin_expires_at FROM users WHERE id = ?');
    $stmt->bind_param('i', $user['id']);
    $stmt->execute();
    $result = $stmt->get_result();
    $userPinRow = $result->fetch_assoc();
    error_log('DEBUG: DB PIN=' . ($userPinRow['pin'] ?? 'NULL') . ', DB pin_expires_at=' . ($userPinRow['pin_expires_at'] ?? 'NULL'));
    if (!$userPinRow || $userPinRow['pin'] !== $pin) {
        error_log('DEBUG: PIN mismatch or not found.');
        returnResponse([
            'success' => false,
            'error' => 'invalid_pin',
            'message' => 'Incorrect PIN. Please check and try again.'
        ]);
    }
    if (empty($userPinRow['pin_expires_at']) || strtotime($userPinRow['pin_expires_at']) < time()) {
        error_log('DEBUG: PIN expired or missing expiry.');
        returnResponse([
            'success' => false,
            'error' => 'pin_expired',
            'message' => 'Your PIN has expired. Please contact admin for a new PIN.'
        ]);
    }
}

// Enhanced single-session device management
if (empty($user['device_id'])) {
    // First login: save device ID
    $updateDeviceStmt = $conn->prepare("UPDATE users SET device_id = ?, last_login = NOW() WHERE id = ?");
    $updateDeviceStmt->bind_param("si", $deviceId, $user['id']);
    $updateDeviceStmt->execute();
    $updateDeviceStmt->close();

    $user['device_id'] = $deviceId;
    $forcedLogout = false;

    // Log first login
    error_log("First login: User {$user['id']} ({$user['name']}) - Device ID: {$deviceId}");

} elseif ($user['device_id'] !== $deviceId) {
    // Device mismatch - forced logout of old device

    // Log the device change
    error_log("Device change: User {$user['id']} ({$user['name']}) - Old device: {$user['device_id']}, New device: {$deviceId}");

    // Invalidate ALL tokens for this user (logout old device)
    $delTokenStmt = $conn->prepare("DELETE FROM api_tokens WHERE user_id = ?");
    $delTokenStmt->bind_param("i", $user['id']);
    $delTokenStmt->execute();
    $deletedTokens = $delTokenStmt->affected_rows;
    $delTokenStmt->close();

    // Update device_id to new device and last login
    $updateDeviceStmt = $conn->prepare("UPDATE users SET device_id = ?, last_login = NOW() WHERE id = ?");
    $updateDeviceStmt->bind_param("si", $deviceId, $user['id']);
    $updateDeviceStmt->execute();
    $updateDeviceStmt->close();

    $user['device_id'] = $deviceId;
    $forcedLogout = true;

    // Log the forced logout
    error_log("Forced logout: User {$user['id']} - Invalidated {$deletedTokens} tokens from old device");

} else {
    // Same device - update last login
    $updateLoginStmt = $conn->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
    $updateLoginStmt->bind_param("i", $user['id']);
    $updateLoginStmt->execute();
    $updateLoginStmt->close();

    $forcedLogout = false;
}

// Successful login
// Generate and store token in database
$tokenData = generateToken($user['id']);
$token = $tokenData['token'];

// Generate JWT token using the improved JWT library
require_once '../includes/jwt.php';

// Determine token expiration based on request
$extendedSession = isset($input['extended_session']) && $input['extended_session'] === true;
$rememberMe = isset($input['remember_me']) && $input['remember_me'] === true;

// Set token expiration
if ($extendedSession || $rememberMe) {
    $tokenExpiry = time() + (300 * 24 * 60 * 60); // 300 days for persistent login
} else {
    $tokenExpiry = time() + (7 * 24 * 60 * 60); // 7 days for regular login
}

$jwtPayload = [
    'user_id' => $user['id'],
    'name' => $user['name'],
    'iat' => time(),
    'exp' => $tokenExpiry,
    'extended' => $extendedSession || $rememberMe
];

// Create JWT token using the improved library
$jwtToken = generate_jwt($jwtPayload, APP_SECRET);

// Enhanced logging in development mode
if (defined('DEV_MODE') && DEV_MODE === true) {
    error_log('=== JWT TOKEN GENERATION ===');
    error_log('User ID: ' . $user['id']);
    error_log('User Name: ' . $user['name']);
    error_log('Current time: ' . time() . ' (' . date('Y-m-d H:i:s') . ')');
    error_log('Expiry time: ' . ($jwtPayload['exp']) . ' (' . date('Y-m-d H:i:s', $jwtPayload['exp']) . ')');
    error_log('Time until expiry: ' . ($jwtPayload['exp'] - time()) . ' seconds');
    error_log('APP_SECRET (first 10 chars): ' . substr(APP_SECRET, 0, 10) . '...');
    error_log('Generated database token: ' . $token);
    error_log('Generated JWT token (first 50 chars): ' . substr($jwtToken, 0, 50) . '...');
    error_log('Token expires at: ' . $tokenData['expires_at']);
    error_log('=== END JWT TOKEN GENERATION ===');
}

// Generate refresh token for extended sessions
$refreshToken = null;
if ($extendedSession || $rememberMe) {
    $refreshTokenPayload = [
        'user_id' => $user['id'],
        'type' => 'refresh',
        'iat' => time(),
        'exp' => time() + (365 * 24 * 60 * 60) // 1 year for refresh token
    ];
    $refreshToken = generate_jwt($refreshTokenPayload, APP_SECRET . '_refresh');
}

returnResponse([
    'success' => true,
    'token' => $jwtToken,
    'refresh_token' => $refreshToken,
    'expires_at' => date('Y-m-d H:i:s', $tokenExpiry),
    'extended_session' => $extendedSession || $rememberMe,
    'forced_logout' => $forcedLogout,
    'session_info' => [
        'device_id' => $user['device_id'],
        'login_time' => date('Y-m-d H:i:s'),
        'session_type' => $forcedLogout ? 'new_device' : 'same_device',
        'previous_device_logout' => $forcedLogout,
    ],
    'user' => [
        'id' => $user['id'],
        'name' => $user['name'],
        'username' => $user['username'],
        'email' => $user['email'],
        'phone_number' => $user['phone_number'],
    ]
]);
