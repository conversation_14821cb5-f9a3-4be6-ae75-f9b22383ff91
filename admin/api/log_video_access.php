<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'config.php';

// Initialize database connection
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Database connection failed'
    ]);
    exit;
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        throw new Exception('Invalid JSON input');
    }

    $vimeoId = $input['vimeo_id'] ?? null;
    $videoId = $input['video_id'] ?? null;
    $userId = $input['user_id'] ?? null;
    $action = $input['action'] ?? null;
    $timestamp = $input['timestamp'] ?? null;
    $appDomain = $input['app_domain'] ?? null;

    if (!$vimeoId || !$videoId || !$userId || !$action) {
        throw new Exception('Missing required parameters');
    }

    // Log the video access
    $logId = logVideoAccess($pdo, $vimeoId, $videoId, $userId, $action, $timestamp, $appDomain);

    if ($logId) {
        // Update video analytics
        updateVideoAnalytics($pdo, $videoId, $action);

        echo json_encode([
            'success' => true,
            'log_id' => $logId,
            'message' => 'Video access logged successfully'
        ]);
    } else {
        throw new Exception('Failed to log video access');
    }

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

function logVideoAccess($pdo, $vimeoId, $videoId, $userId, $action, $timestamp, $appDomain) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO video_access_logs (
                vimeo_id, video_id, user_id, action, timestamp,
                app_domain, ip_address, user_agent, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ");

        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        $logTimestamp = $timestamp ? date('Y-m-d H:i:s', strtotime($timestamp)) : date('Y-m-d H:i:s');

        $stmt->execute([
            $vimeoId,
            $videoId,
            $userId,
            $action,
            $logTimestamp,
            $appDomain,
            $ipAddress,
            $userAgent
        ]);

        return $pdo->lastInsertId();

    } catch (Exception $e) {
        error_log("Failed to log video access: " . $e->getMessage());
        return null;
    }
}

function updateVideoAnalytics($pdo, $videoId, $action) {
    try {
        // Update or create analytics record for this video
        $stmt = $pdo->prepare("
            INSERT INTO video_analytics (video_id, total_views, total_completions, last_updated)
            VALUES (?, ?, ?, NOW())
            ON DUPLICATE KEY UPDATE
                total_views = total_views + CASE WHEN ? = 'play' THEN 1 ELSE 0 END,
                total_completions = total_completions + CASE WHEN ? = 'complete' THEN 1 ELSE 0 END,
                last_updated = NOW()
        ");

        $initialViews = ($action === 'play') ? 1 : 0;
        $initialCompletions = ($action === 'complete') ? 1 : 0;

        $stmt->execute([
            $videoId,
            $initialViews,
            $initialCompletions,
            $action,
            $action
        ]);

        return true;

    } catch (Exception $e) {
        error_log("Failed to update video analytics: " . $e->getMessage());
        return false;
    }
}

function getVideoAccessStats($pdo, $videoId, $timeframe = '30 days') {
    try {
        $stmt = $pdo->prepare("
            SELECT
                action,
                COUNT(*) as count,
                DATE(created_at) as date
            FROM video_access_logs
            WHERE video_id = ?
                AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
            GROUP BY action, DATE(created_at)
            ORDER BY date DESC
        ");

        $days = match($timeframe) {
            '7 days' => 7,
            '30 days' => 30,
            '90 days' => 90,
            default => 30
        };

        $stmt->execute([$videoId, $days]);

        return $stmt->fetchAll(PDO::FETCH_ASSOC);

    } catch (Exception $e) {
        error_log("Failed to get video access stats: " . $e->getMessage());
        return [];
    }
}

function detectSuspiciousActivity($pdo, $userId, $timeWindow = 300) {
    try {
        // Check for too many access attempts in a short time
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as attempt_count
            FROM video_access_logs
            WHERE user_id = ?
                AND created_at >= DATE_SUB(NOW(), INTERVAL ? SECOND)
        ");

        $stmt->execute([$userId, $timeWindow]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        $attemptCount = $result['attempt_count'] ?? 0;

        // Flag as suspicious if more than 50 attempts in 5 minutes
        if ($attemptCount > 50) {
            logSuspiciousActivity($pdo, $userId, 'excessive_access_attempts', $attemptCount);
            return true;
        }

        return false;

    } catch (Exception $e) {
        error_log("Failed to detect suspicious activity: " . $e->getMessage());
        return false;
    }
}

function logSuspiciousActivity($pdo, $userId, $type, $details) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO security_alerts (user_id, alert_type, details, ip_address, user_agent, created_at)
            VALUES (?, ?, ?, ?, ?, NOW())
        ");

        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';

        $stmt->execute([
            $userId,
            $type,
            json_encode($details),
            $ipAddress,
            $userAgent
        ]);

        return true;

    } catch (Exception $e) {
        error_log("Failed to log suspicious activity: " . $e->getMessage());
        return false;
    }
}

// Create necessary tables if they don't exist
function createAnalyticsTables($pdo) {
    try {
        // Video analytics table
        $sql1 = "
        CREATE TABLE IF NOT EXISTS video_analytics (
            id INT AUTO_INCREMENT PRIMARY KEY,
            video_id INT NOT NULL UNIQUE,
            total_views INT DEFAULT 0,
            total_completions INT DEFAULT 0,
            last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_video_id (video_id)
        )";

        // Security alerts table
        $sql2 = "
        CREATE TABLE IF NOT EXISTS security_alerts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            alert_type VARCHAR(100) NOT NULL,
            details JSON,
            ip_address VARCHAR(45),
            user_agent TEXT,
            resolved_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_alert_type (alert_type),
            INDEX idx_created_at (created_at)
        )";

        // Update video_access_logs table to include new fields
        $sql3 = "
        ALTER TABLE video_access_logs
        ADD COLUMN IF NOT EXISTS vimeo_id VARCHAR(50),
        ADD COLUMN IF NOT EXISTS timestamp TIMESTAMP NULL,
        ADD COLUMN IF NOT EXISTS app_domain VARCHAR(255)
        ";

        $pdo->exec($sql1);
        $pdo->exec($sql2);
        $pdo->exec($sql3);

    } catch (Exception $e) {
        error_log("Failed to create analytics tables: " . $e->getMessage());
    }
}

// Initialize tables and check for suspicious activity
createAnalyticsTables($pdo);

// Check for suspicious activity if user_id is provided
if (isset($input['user_id'])) {
    detectSuspiciousActivity($pdo, $input['user_id']);
}
?>
