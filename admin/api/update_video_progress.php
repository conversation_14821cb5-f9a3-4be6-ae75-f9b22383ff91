<?php
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/utilities.php';
require_once '../includes/auth.php';
require_once '../includes/cors.php'; // Include CORS middleware

// Set content type header
header('Content-Type: application/json');

// Check if request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    returnError('Method not allowed', 405);
}

// Get JSON data from request body
$jsonData = file_get_contents('php://input');
$data = json_decode($jsonData, true);

// Check if data is valid JSON
if (!$data) {
    returnError('Invalid JSON data');
}

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Check if user is authenticated
$auth = new Auth($conn);
$userId = $auth->authenticateToken();

if (!$userId) {
    returnError('Unauthorized access', 401);
}

// Check required fields
if (!isset($data['video_id']) || !is_numeric($data['video_id'])) {
    returnError('Video ID is required');
}

$videoId = $data['video_id'];
$watchDuration = isset($data['watch_duration_seconds']) ? intval($data['watch_duration_seconds']) : null;
$lastPosition = isset($data['last_position_seconds']) ? intval($data['last_position_seconds']) : null;
$isCompleted = isset($data['is_completed']) ? (bool)$data['is_completed'] : null;

// Check if video exists and is unlocked for this user
$videoQuery = "SELECT v.*, p.is_unlocked, p.id as progress_id
               FROM course_videos v
               LEFT JOIN user_video_progress p ON v.id = p.video_id AND p.user_id = ?
               WHERE v.id = ?";
$videoStmt = $conn->prepare($videoQuery);
$videoStmt->bind_param("ii", $userId, $videoId);
$videoStmt->execute();
$videoResult = $videoStmt->get_result();

if ($videoResult->num_rows === 0) {
    returnError('Video not found', 404);
}

$video = $videoResult->fetch_assoc();

// Check if user is enrolled in the course
$enrollmentQuery = "SELECT * FROM user_course_enrollments
                   WHERE user_id = ? AND course_id = ? AND status = 'active'";
$enrollmentStmt = $conn->prepare($enrollmentQuery);
$enrollmentStmt->bind_param("ii", $userId, $video['course_id']);
$enrollmentStmt->execute();
$enrollmentResult = $enrollmentStmt->get_result();

if ($enrollmentResult->num_rows === 0) {
    returnError('You are not enrolled in this course', 403);
}

// Check if video is unlocked
if (!$video['is_unlocked']) {
    returnError('This video is locked', 403);
}

// Update or insert progress
if ($video['progress_id']) {
    // Update existing progress
    $updateQuery = "UPDATE user_video_progress SET ";
    $updateParams = [];
    $updateTypes = "";

    if ($watchDuration !== null) {
        $updateQuery .= "watch_duration_seconds = ?, ";
        $updateParams[] = $watchDuration;
        $updateTypes .= "i";
    }

    if ($lastPosition !== null) {
        $updateQuery .= "last_position_seconds = ?, ";
        $updateParams[] = $lastPosition;
        $updateTypes .= "i";
    }

    if ($isCompleted !== null) {
        $updateQuery .= "is_completed = ?, completion_date = " . ($isCompleted ? "NOW()" : "NULL") . ", ";
        $updateParams[] = $isCompleted ? 1 : 0;
        $updateTypes .= "i";
    }

    // Remove trailing comma and space
    $updateQuery = rtrim($updateQuery, ", ");

    $updateQuery .= " WHERE id = ?";
    $updateParams[] = $video['progress_id'];
    $updateTypes .= "i";

    $updateStmt = $conn->prepare($updateQuery);

    // Dynamically bind parameters
    if (!empty($updateParams)) {
        $bindParams = array_merge([$updateTypes], $updateParams);
        $bindParamsRef = [];
        foreach ($bindParams as $key => $value) {
            $bindParamsRef[$key] = &$bindParams[$key];
        }
        call_user_func_array([$updateStmt, 'bind_param'], $bindParamsRef);

        if (!$updateStmt->execute()) {
            returnError('Failed to update progress: ' . $conn->error);
        }
    }
} else {
    // Insert new progress
    $insertQuery = "INSERT INTO user_video_progress
                   (user_id, video_id, is_unlocked, is_completed, unlock_date, completion_date,
                    watch_duration_seconds, last_position_seconds)
                   VALUES (?, ?, 1, ?, CURDATE(), " . ($isCompleted ? "NOW()" : "NULL") . ", ?, ?)";
    $insertStmt = $conn->prepare($insertQuery);
    $isCompletedValue = $isCompleted ? 1 : 0;
    $watchDurationValue = $watchDuration ?? 0;
    $lastPositionValue = $lastPosition ?? 0;
    $insertStmt->bind_param("iiiii", $userId, $videoId, $isCompletedValue, $watchDurationValue, $lastPositionValue);

    if (!$insertStmt->execute()) {
        returnError('Failed to insert progress: ' . $conn->error);
    }
}

// Log activity for analytics
$activityQuery = "INSERT INTO user_activity_log
                 (user_id, activity_type, related_id, details, created_at)
                 VALUES (?, 'video_progress', ?, ?, NOW())";
$details = json_encode([
    'video_id' => $videoId,
    'course_id' => $video['course_id'],
    'watch_duration' => $watchDuration,
    'last_position' => $lastPosition,
    'is_completed' => $isCompleted
]);
$activityStmt = $conn->prepare($activityQuery);
$activityStmt->bind_param("iis", $userId, $videoId, $details);
$activityStmt->execute();

// Return success response
returnResponse([
    'success' => true,
    'message' => 'Progress updated successfully',
    'data' => [
        'video_id' => $videoId,
        'watch_duration_seconds' => $watchDuration,
        'last_position_seconds' => $lastPosition,
        'is_completed' => $isCompleted,
        'course_id' => $video['course_id']
    ]
]);

// Use the global helper functions from config.php
?>
