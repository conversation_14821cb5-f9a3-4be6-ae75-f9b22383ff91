<?php
require_once 'includes/config.php';
require_once 'includes/auth.php';
require_once 'includes/database.php';
require_once 'includes/utilities.php';

// Initialize auth
$auth = new Auth();

// Check if user is logged in
if (!$auth->isLoggedIn()) {
    Utilities::setFlashMessage('error', 'You must be logged in to access this page.');
    Utilities::redirect('login.php');
    exit;
}

// Check if user has admin role
if (!$auth->hasRole('admin')) {
    Utilities::setFlashMessage('error', 'You do not have permission to access this page.');
    Utilities::redirect('index.php');
    exit;
}

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Get course ID and user ID from the URL parameters
$courseId = isset($_GET['course_id']) ? intval($_GET['course_id']) : 0;
$userId = isset($_GET['user_id']) ? intval($_GET['user_id']) : 0;

// Redirect to users.php if no IDs are provided
if ($courseId === 0 || $userId === 0) {
    Utilities::setFlashMessage('error', 'Please select a user first.');
    Utilities::redirect('users.php');
    exit;
}

// Get analytics data if course ID and user ID are provided
$analytics = [];
$videos = [];
$selectedCourse = null;
$selectedUser = null;
$debugInfo = [];

if ($courseId > 0 && $userId > 0) {
    // Get course details
    $courseQuery = "SELECT * FROM courses WHERE id = ?";
    $courseStmt = $conn->prepare($courseQuery);
    $courseStmt->bind_param("i", $courseId);
    $courseStmt->execute();
    $courseResult = $courseStmt->get_result();
    $selectedCourse = $courseResult->fetch_assoc();

    // Get user details
    $userQuery = "SELECT * FROM users WHERE id = ?";
    $userStmt = $conn->prepare($userQuery);
    $userStmt->bind_param("i", $userId);
    $userStmt->execute();
    $userResult = $userStmt->get_result();
    $selectedUser = $userResult->fetch_assoc();

    // Get enrollment details
    $enrollmentQuery = "SELECT e.*,
                       DATEDIFF(CURRENT_DATE, e.start_date) as days_enrolled,
                       DATEDIFF(e.end_date, CURRENT_DATE) as days_remaining,
                       DATEDIFF(CURRENT_DATE, e.start_date) DIV 7 as weeks_enrolled
                       FROM user_course_enrollments e
                       WHERE e.user_id = ? AND e.course_id = ?";
    $enrollmentStmt = $conn->prepare($enrollmentQuery);
    $enrollmentStmt->bind_param("ii", $userId, $courseId);
    $enrollmentStmt->execute();
    $enrollmentResult = $enrollmentStmt->get_result();
    $enrollment = $enrollmentResult->fetch_assoc();

    // Get videos with progress
    $videosQuery = "SELECT v.*,
                   IFNULL(p.is_unlocked, 0) as is_unlocked,
                   IFNULL(p.is_completed, 0) as is_completed,
                   p.unlock_date, p.completion_date,
                   p.watch_duration_seconds, p.last_position_seconds,
                   (SELECT COUNT(*) FROM user_activity_log
                    WHERE user_id = ? AND activity_type = 'video_progress'
                    AND related_id = v.id) as view_count
                   FROM course_videos v
                   LEFT JOIN user_video_progress p ON v.id = p.video_id AND p.user_id = ?
                   WHERE v.course_id = ?
                   ORDER BY v.week_number, v.sequence_number";
    $videosStmt = $conn->prepare($videosQuery);
    $videosStmt->bind_param("iii", $userId, $userId, $courseId);
    $videosStmt->execute();
    $videosResult = $videosStmt->get_result();

    while ($video = $videosResult->fetch_assoc()) {
        $videos[] = $video;
    }

    // Debug: Check if user_activity_log table exists and has data
    $debugQuery = "SHOW TABLES LIKE 'user_activity_log'";
    $debugResult = $conn->query($debugQuery);
    $debugInfo['table_exists'] = $debugResult->num_rows > 0;

    if ($debugInfo['table_exists']) {
        // Check total records in activity log
        $countQuery = "SELECT COUNT(*) as total_records FROM user_activity_log";
        $countResult = $conn->query($countQuery);
        $debugInfo['total_activity_records'] = $countResult->fetch_assoc()['total_records'];

        // Check records for this user
        $userCountQuery = "SELECT COUNT(*) as user_records FROM user_activity_log WHERE user_id = ?";
        $userCountStmt = $conn->prepare($userCountQuery);
        $userCountStmt->bind_param("i", $userId);
        $userCountStmt->execute();
        $debugInfo['user_activity_records'] = $userCountStmt->get_result()->fetch_assoc()['user_records'];

        // Check video_progress records for this user
        $videoProgressQuery = "SELECT COUNT(*) as video_progress_records FROM user_activity_log WHERE user_id = ? AND activity_type = 'video_progress'";
        $videoProgressStmt = $conn->prepare($videoProgressQuery);
        $videoProgressStmt->bind_param("i", $userId);
        $videoProgressStmt->execute();
        $debugInfo['video_progress_records'] = $videoProgressStmt->get_result()->fetch_assoc()['video_progress_records'];
    }

    // Get overall analytics - Fixed to handle both JSON and TEXT column types
    $analyticsQuery = "SELECT
                      COUNT(DISTINCT al.id) as total_views,
                      SUM(CASE
                          WHEN al.details LIKE '%watch_duration%' THEN
                              CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(al.details, '\"watch_duration\":', -1), ',', 1) AS UNSIGNED)
                          ELSE 0
                      END) as total_watch_duration,
                      COUNT(DISTINCT CASE
                          WHEN al.details LIKE '%\"is_completed\":true%' OR al.details LIKE '%\"is_completed\":1%'
                          THEN al.related_id
                      END) as completed_videos,
                      MAX(al.created_at) as last_activity
                      FROM user_activity_log al
                      JOIN course_videos cv ON al.related_id = cv.id
                      WHERE al.user_id = ? AND cv.course_id = ? AND al.activity_type = 'video_progress'
                      GROUP BY al.user_id, cv.course_id";
    $analyticsStmt = $conn->prepare($analyticsQuery);
    $analyticsStmt->bind_param("ii", $userId, $courseId);
    $analyticsStmt->execute();
    $analyticsResult = $analyticsStmt->get_result();
    $analytics = $analyticsResult->fetch_assoc();

    // Calculate progress percentage
    $completedCount = $analytics['completed_videos'] ?? 0;
    $totalVideos = count($videos);
    $progressPercentage = $totalVideos > 0 ? round(($completedCount / $totalVideos) * 100) : 0;

    // Get activity timeline - Fixed to handle both JSON and TEXT column types
    $timelineQuery = "SELECT
                     al.*,
                     cv.title as video_title,
                     cv.week_number,
                     al.details,
                     CASE
                         WHEN al.details LIKE '%watch_duration%' THEN
                             CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(al.details, '\"watch_duration\":', -1), ',', 1) AS UNSIGNED)
                         ELSE 0
                     END as watch_duration,
                     CASE
                         WHEN al.details LIKE '%last_position%' THEN
                             CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(al.details, '\"last_position\":', -1), ',', 1) AS UNSIGNED)
                         ELSE 0
                     END as last_position,
                     CASE
                         WHEN al.details LIKE '%\"is_completed\":true%' OR al.details LIKE '%\"is_completed\":1%' THEN 1
                         ELSE 0
                     END as is_completed,
                     CASE
                         WHEN al.details LIKE '%\"action\":\"%' THEN
                             SUBSTRING_INDEX(SUBSTRING_INDEX(al.details, '\"action\":\"', -1), '\"', 1)
                         ELSE 'unknown'
                     END as action
                     FROM user_activity_log al
                     JOIN course_videos cv ON al.related_id = cv.id
                     WHERE al.user_id = ? AND cv.course_id = ? AND al.activity_type = 'video_progress'
                     ORDER BY al.created_at DESC
                     LIMIT 50";
    $timelineStmt = $conn->prepare($timelineQuery);
    $timelineStmt->bind_param("ii", $userId, $courseId);
    $timelineStmt->execute();
    $timelineResult = $timelineStmt->get_result();
    $timeline = [];

    while ($activity = $timelineResult->fetch_assoc()) {
        $timeline[] = $activity;
    }

    // Debug: Store timeline count
    $debugInfo['timeline_count'] = count($timeline);

    // Debug: Get sample activity records without JOIN to see if data exists
    $sampleQuery = "SELECT al.*, al.details FROM user_activity_log al WHERE al.user_id = ? AND al.activity_type = 'video_progress' ORDER BY al.created_at DESC LIMIT 5";
    $sampleStmt = $conn->prepare($sampleQuery);
    $sampleStmt->bind_param("i", $userId);
    $sampleStmt->execute();
    $sampleResult = $sampleStmt->get_result();
    $debugInfo['sample_activities'] = [];
    while ($sample = $sampleResult->fetch_assoc()) {
        $debugInfo['sample_activities'][] = $sample;
    }
}

// Include header
$pageTitle = 'Video Analytics Dashboard';
require_once 'includes/header.php';
?>

<!-- Custom CSS for modern design -->
<style>
.analytics-header {
    background: linear-gradient(135deg, #1a1f36 0%, #2d3748 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
    border-radius: 0 0 20px 20px;
}

.analytics-header h1 {
    font-weight: 600;
    letter-spacing: -0.5px;
}

.header-actions .btn {
    width: 36px;
    height: 36px;
    padding: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: white;
    transition: all 0.2s ease;
}

.header-actions .btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.analytics-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border: none;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.analytics-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.stat-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-card:nth-child(2) .stat-icon {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-card:nth-child(3) .stat-icon {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.stat-card:nth-child(4) .stat-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-content {
    flex: 1;
}

.stat-content h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
    line-height: 1.2;
    color: #2d3748;
}

.stat-content p {
    margin: 0;
    font-size: 0.875rem;
    color: #718096;
}

.timeline-modern {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.timeline-item-modern {
    display: flex;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f8f9ff;
    border-radius: 10px;
    border-left: 4px solid #667eea;
}

.video-progress-modern {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    margin-bottom: 2rem;
}

.progress-bar-modern {
    height: 8px;
    border-radius: 10px;
    background: #e9ecef;
    overflow: hidden;
}

.progress-bar-modern .progress {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 10px;
    transition: width 0.3s ease;
}

.btn-modern {
    border-radius: 25px;
    padding: 0.5rem 1.5rem;
    font-weight: 500;
    border: none;
    transition: all 0.3s ease;
}

.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* Modern Video Progress Styles */
.video-progress-modern {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.05);
}

.video-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    position: relative;
    overflow: hidden;
}

.video-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.video-status-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    z-index: 1;
}

.video-status-badge span {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.9rem;
}

.status-locked { background: #6c757d; }
.status-completed { background: #28a745; }
.status-in-progress { background: #007bff; }

.video-content {
    padding: 1.5rem;
}

.video-header {
    margin-bottom: 1rem;
}

.week-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    background: #f8f9fa;
    border-radius: 20px;
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
}

.video-title {
    font-size: 1rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
    line-height: 1.4;
}

.video-progress {
    margin-bottom: 1rem;
}

.progress-bar-modern {
    height: 6px;
    background: #f0f0f0;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-bar-modern .progress {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #00c6ff);
    border-radius: 3px;
    transition: width 0.3s ease;
}

.progress-stats {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    color: #6c757d;
}

.progress-stats span {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.video-action {
    margin-top: 1rem;
}

.video-action .btn {
    padding: 0.6rem 1rem;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    border: none;
    transition: all 0.2s ease;
}

.btn-unlock {
    background: #e9ecef;
    color: #495057;
}

.btn-unlock:hover {
    background: #dee2e6;
    color: #212529;
}

.btn-complete {
    background: #007bff;
    color: white;
}

.btn-complete:hover {
    background: #0056b3;
}

.btn-completed {
    background: #28a745;
    color: white;
    opacity: 0.8;
}

@media (max-width: 768px) {
    .video-progress-modern {
        padding: 1rem;
    }
    
    .video-card {
        margin-bottom: 1rem;
    }
}

/* Modern Analytics Header */
.analytics-header {
    background: linear-gradient(135deg, #1a1f36 0%, #2d3748 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
    border-radius: 0 0 20px 20px;
}

.header-content {
    position: relative;
}

.analytics-header h1 {
    font-weight: 600;
    letter-spacing: -0.5px;
    color: white;
}

.header-actions .btn {
    width: 36px;
    height: 36px;
    padding: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: white;
    transition: all 0.2s ease;
}

.header-actions .btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* User Profile Card */
.user-profile-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 1.5rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.user-info {
    flex: 1;
}

.user-name {
    font-size: 1.5rem;
    font-weight: 600;
    color: white;
    margin: 0;
}

.user-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-top: 0.5rem;
}

.user-meta .badge {
    padding: 0.5rem 0.75rem;
    font-weight: 500;
    font-size: 0.875rem;
}

.course-info {
    padding-left: 1rem;
    border-left: 1px solid rgba(255, 255, 255, 0.1);
}

.course-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: white;
    margin: 0;
}

.course-meta {
    margin-top: 0.5rem;
}

.course-meta .badge {
    padding: 0.5rem 0.75rem;
    font-weight: 500;
    font-size: 0.875rem;
}

@media (max-width: 768px) {
    .analytics-header {
        padding: 1.5rem 0;
    }

    .user-profile-card {
        padding: 1rem;
    }

    .user-avatar {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }

    .user-name {
        font-size: 1.25rem;
    }

    .course-info {
        padding-left: 0;
        border-left: none;
        margin-top: 1rem;
        text-align: left;
    }

    .user-meta {
        flex-wrap: wrap;
    }
}

/* Responsive Side Panel Styles */
@media (max-width: 991.98px) {
    .course-info {
        padding-left: 0;
        border-left: none;
        margin-top: 1.5rem;
        text-align: left;
    }

    .user-profile-card {
        padding: 1.25rem;
    }

    .user-profile-card .row {
        flex-direction: column;
    }

    .user-profile-card .col-md-8,
    .user-profile-card .col-md-4 {
        width: 100%;
        padding: 0;
    }

    .course-meta {
        margin-top: 1rem;
    }

    .course-meta .badge {
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 767.98px) {
    .analytics-header {
        padding: 1.25rem 0;
        margin-bottom: 1.5rem;
    }

    .header-content {
        padding: 0 0.5rem;
    }

    .user-profile-card {
        padding: 1rem;
        margin: 0 -0.5rem;
        border-radius: 0;
    }

    .user-avatar {
        width: 45px;
        height: 45px;
        font-size: 1.25rem;
    }

    .user-name {
        font-size: 1.25rem;
    }

    .user-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .user-meta .badge {
        margin-right: 0;
    }

    .course-title {
        font-size: 1.1rem;
    }

    .course-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .course-meta .badge {
        margin-right: 0;
    }

    .header-actions {
        position: absolute;
        top: 0;
        right: 0.5rem;
    }

    .header-actions .btn {
        width: 32px;
        height: 32px;
    }
}

@media (max-width: 575.98px) {
    .analytics-header h1 {
        font-size: 1.5rem;
    }

    .user-profile-card {
        padding: 0.75rem;
    }

    .user-avatar {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .user-name {
        font-size: 1.1rem;
    }

    .course-title {
        font-size: 1rem;
    }

    .course-meta .badge {
        font-size: 0.75rem;
        padding: 0.4rem 0.6rem;
    }
}

/* Add smooth transitions */
.user-profile-card,
.user-avatar,
.user-name,
.course-info,
.course-meta .badge {
    transition: all 0.3s ease;
}

/* Improve touch targets on mobile */
@media (max-width: 767.98px) {
    .header-actions .btn,
    .user-meta .badge,
    .course-meta .badge {
        min-height: 36px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }
}

/* Ensure proper spacing in flex containers */
.user-meta,
.course-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

/* Improve readability on small screens */
@media (max-width: 575.98px) {
    .text-white-50 {
        font-size: 0.875rem;
    }
}
</style>

<div class="container-fluid py-4">
    <!-- Header Section -->
    <div class="analytics-header">
        <div class="container">
            <div class="header-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="display-6 mb-0">Video Analytics</h1>
                    <div class="header-actions">
                        <?php if ($selectedUser): ?>
                            <a href="user_edit.php?id=<?php echo $selectedUser['id']; ?>" class="btn btn-light btn-sm me-2">
                                <i class="fas fa-user-edit"></i>
                            </a>
                        <?php endif; ?>
                        <a href="users.php" class="btn btn-light btn-sm">
                            <i class="fas fa-arrow-left"></i>
                        </a>
                    </div>
                </div>

                <?php if ($selectedUser && $selectedCourse): ?>
                    <div class="user-profile-card">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <div class="d-flex align-items-center">
                                    <div class="user-avatar">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div class="user-info ms-3">
                                        <h2 class="user-name mb-1"><?php echo htmlspecialchars($selectedUser['name']); ?></h2>
                                        <div class="user-meta">
                                            <span class="badge bg-primary me-2">
                                                <i class="fas fa-user me-1"></i>Client
                                            </span>
                                            <span class="text-white-50">
                                                <i class="fas fa-envelope me-1"></i><?php echo htmlspecialchars($selectedUser['email']); ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="course-info text-end">
                                    <h3 class="course-title mb-2"><?php echo htmlspecialchars($selectedCourse['title']); ?></h3>
                                    <div class="course-meta">
                                        <span class="badge bg-light text-dark me-2">
                                            <i class="fas fa-clock me-1"></i><?php echo $selectedCourse['duration_weeks']; ?> Weeks
                                        </span>
                                        <span class="badge bg-light text-dark">
                                            <i class="fas fa-book me-1"></i><?php echo count($videos); ?> Videos
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Navigation Buttons -->
    <div class="d-flex justify-content-end mb-4">
        <?php if ($selectedUser): ?>
            <a href="user_edit.php?id=<?php echo $selectedUser['id']; ?>" class="btn btn-outline-info btn-modern me-2">
                <i class="fas fa-user-edit me-1"></i>Edit User
            </a>
        <?php endif; ?>
        <a href="users.php" class="btn btn-outline-secondary btn-modern">
            <i class="fas fa-arrow-left me-1"></i>Back
        </a>
    </div>

    <?php if ($selectedCourse && $selectedUser): ?>
        <!-- Analytics Overview Cards -->
        <div class="stats-container">
            <div class="row g-3">
                <div class="col-xl-3 col-md-6">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-eye"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo $analytics['total_views'] ?? 0; ?></h3>
                            <p>Total Views</p>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h3>
                                <?php
                                $completedCount = $analytics['completed_videos'] ?? 0;
                                $totalVideos = count($videos);
                                echo "$completedCount / $totalVideos";
                                ?>
                            </h3>
                            <p>Completed</p>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h3>
                                <?php
                                $totalSeconds = $analytics['total_watch_duration'] ?? 0;
                                $hours = floor($totalSeconds / 3600);
                                $minutes = floor(($totalSeconds % 3600) / 60);
                                echo "$hours h $minutes m";
                                ?>
                            </h3>
                            <p>Watch Time</p>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-calendar-check"></i>
                        </div>
                        <div class="stat-content">
                            <h3>
                                <?php
                                echo $analytics['last_activity'] ? date('M d', strtotime($analytics['last_activity'])) : 'N/A';
                                ?>
                            </h3>
                            <p>Last Activity</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Course Overview -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="analytics-card h-100">
                    <div class="card-header bg-transparent border-0 py-3">
                        <h5 class="m-0 fw-bold text-dark">
                            <i class="fas fa-chart-pie me-2 text-success"></i>Progress Overview
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Progress Circle -->
                        <div class="text-center mb-4">
                            <div class="position-relative d-inline-block">
                                <svg width="120" height="120" class="progress-ring">
                                    <circle cx="60" cy="60" r="50" stroke="#e9ecef" stroke-width="8" fill="transparent"/>
                                    <circle cx="60" cy="60" r="50" stroke="url(#gradient)" stroke-width="8" fill="transparent"
                                            stroke-dasharray="314" stroke-dashoffset="<?php echo 314 - (314 * $progressPercentage / 100); ?>"
                                            stroke-linecap="round" transform="rotate(-90 60 60)"/>
                                    <defs>
                                        <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                            <stop offset="0%" style="stop-color:#667eea"/>
                                            <stop offset="100%" style="stop-color:#764ba2"/>
                                        </linearGradient>
                                    </defs>
                                </svg>
                                <div class="position-absolute top-50 start-50 translate-middle text-center">
                                    <h3 class="mb-0 fw-bold text-primary"><?php echo $progressPercentage; ?>%</h3>
                                    <small class="text-muted">Complete</small>
                                </div>
                            </div>
                        </div>

                        <!-- Progress Stats -->
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="border-end">
                                    <h6 class="fw-bold text-primary"><?php echo $enrollment['days_enrolled']; ?></h6>
                                    <small class="text-muted">Days Enrolled</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="border-end">
                                    <h6 class="fw-bold text-success"><?php echo $enrollment['weeks_enrolled']; ?>/<?php echo $selectedCourse['duration_weeks']; ?></h6>
                                    <small class="text-muted">Weeks Done</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <h6 class="fw-bold text-warning"><?php echo max(0, $enrollment['days_remaining']); ?></h6>
                                <small class="text-muted">Days Left</small>
                            </div>
                        </div>

                        <!-- Timeline Progress -->
                        <div class="mt-4">
                            <div class="d-flex justify-content-between mb-2">
                                <small class="text-muted">Course Timeline</small>
                                <small class="text-muted">Week <?php echo min($enrollment['weeks_enrolled'] + 1, $selectedCourse['duration_weeks']); ?> of <?php echo $selectedCourse['duration_weeks']; ?></small>
                            </div>
                            <div class="progress-bar-modern">
                                <div class="progress" style="width: <?php echo min(100, ($enrollment['weeks_enrolled'] / $selectedCourse['duration_weeks']) * 100); ?>%"></div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex gap-2 mt-4">
                            <a href="user_edit.php?id=<?php echo $userId; ?>" class="btn btn-outline-primary btn-modern flex-fill">
                                <i class="fas fa-edit me-1"></i>Manage
                            </a>
                            <button type="button" class="btn btn-outline-success btn-modern flex-fill" data-bs-toggle="modal" data-bs-target="#unlockVideosModal">
                                <i class="fas fa-unlock me-1"></i>Unlock
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Video Progress -->
        <div class="video-progress-modern">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h4 class="fw-bold text-dark mb-0">
                    <i class="fas fa-video me-2 text-primary"></i>Video Progress
                </h4>
                <div class="d-flex align-items-center gap-3">
                    <span class="badge bg-primary rounded-pill px-3 py-2">
                        <i class="fas fa-percentage me-1"></i><?php echo $progressPercentage; ?>% Complete
                    </span>
                </div>
            </div>

            <!-- Video Cards Grid -->
            <div class="row g-4">
                <?php foreach ($videos as $index => $video): ?>
                    <div class="col-md-6 col-lg-4">
                        <div class="video-card">
                            <!-- Video Status Badge -->
                            <div class="video-status-badge">
                                <?php if (!$video['is_unlocked']): ?>
                                    <span class="status-locked"><i class="fas fa-lock"></i></span>
                                <?php elseif ($video['is_completed']): ?>
                                    <span class="status-completed"><i class="fas fa-check"></i></span>
                                <?php else: ?>
                                    <span class="status-in-progress"><i class="fas fa-play"></i></span>
                                <?php endif; ?>
                            </div>

                            <!-- Video Content -->
                            <div class="video-content">
                                <div class="video-header">
                                    <span class="week-badge">Week <?php echo $video['week_number']; ?></span>
                                    <h6 class="video-title"><?php echo htmlspecialchars($video['title']); ?></h6>
                                </div>

                                <!-- Progress Bar -->
                                <?php if ($video['duration_minutes'] && $video['watch_duration_seconds']): ?>
                                    <?php
                                    $totalVideoSeconds = $video['duration_minutes'] * 60;
                                    $watchPercentage = min(100, round(($video['watch_duration_seconds'] / $totalVideoSeconds) * 100));
                                    ?>
                                    <div class="video-progress">
                                        <div class="progress-bar-modern">
                                            <div class="progress" style="width: <?php echo $watchPercentage; ?>%"></div>
                                        </div>
                                        <div class="progress-stats">
                                            <span class="watch-time">
                                                <i class="fas fa-clock"></i>
                                                <?php echo floor($video['watch_duration_seconds'] / 60); ?>m
                                            </span>
                                            <span class="total-duration">
                                                <i class="fas fa-hourglass"></i>
                                                <?php echo $video['duration_minutes']; ?>m
                                            </span>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <!-- Action Button -->
                                <div class="video-action">
                                    <?php if (!$video['is_unlocked']): ?>
                                        <form action="unlock_video.php" method="post" class="w-100">
                                            <input type="hidden" name="video_id" value="<?php echo $video['id']; ?>">
                                            <input type="hidden" name="user_id" value="<?php echo $userId; ?>">
                                            <input type="hidden" name="course_id" value="<?php echo $courseId; ?>">
                                            <button type="submit" class="btn btn-unlock w-100">
                                                <i class="fas fa-unlock"></i> Unlock
                                            </button>
                                        </form>
                                    <?php elseif (!$video['is_completed']): ?>
                                        <form action="mark_video_completed.php" method="post" class="w-100">
                                            <input type="hidden" name="video_id" value="<?php echo $video['id']; ?>">
                                            <input type="hidden" name="user_id" value="<?php echo $userId; ?>">
                                            <input type="hidden" name="course_id" value="<?php echo $courseId; ?>">
                                            <button type="submit" class="btn btn-complete w-100">
                                                <i class="fas fa-check"></i> Complete
                                            </button>
                                        </form>
                                    <?php else: ?>
                                        <button class="btn btn-completed w-100" disabled>
                                            <i class="fas fa-check-circle"></i> Completed
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        <!-- Activity Timeline -->
        <div class="timeline-modern">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h4 class="fw-bold text-dark mb-0">
                    <i class="fas fa-history me-2 text-primary"></i>Recent Activity Timeline
                </h4>
                <span class="badge bg-info rounded-pill px-3 py-2">
                    <i class="fas fa-clock me-1"></i><?php echo count($timeline); ?> Activities
                </span>
            </div>

            <?php if (empty($timeline)): ?>
                <div class="text-center py-5">
                    <div class="mb-3">
                        <i class="fas fa-chart-line fa-3x text-muted opacity-50"></i>
                    </div>
                    <h5 class="text-muted">No Activity Yet</h5>
                    <p class="text-muted">User activity will appear here once they start watching videos.</p>
                </div>
            <?php else: ?>
                <div class="activity-timeline">
                    <?php foreach ($timeline as $index => $activity): ?>
                        <div class="timeline-item-modern">
                            <div class="activity-icon me-3">
                                <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                    <?php if ($activity['action'] === 'complete'): ?>
                                        <i class="fas fa-check"></i>
                                    <?php elseif ($activity['action'] === 'play'): ?>
                                        <i class="fas fa-play"></i>
                                    <?php else: ?>
                                        <i class="fas fa-eye"></i>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="activity-content flex-grow-1">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <div>
                                        <h6 class="mb-1 fw-bold">
                                            Week <?php echo $activity['week_number']; ?>: <?php echo htmlspecialchars($activity['video_title']); ?>
                                        </h6>
                                        <div class="d-flex align-items-center gap-2 mb-2">
                                            <span class="badge bg-light text-dark rounded-pill">
                                                <i class="fas fa-bolt me-1"></i><?php echo ucfirst($activity['action']); ?>
                                            </span>
                                            <?php if ($activity['is_completed']): ?>
                                                <span class="badge bg-success rounded-pill">
                                                    <i class="fas fa-trophy me-1"></i>Completed
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <div class="fw-bold text-primary"><?php echo date('M d', strtotime($activity['created_at'])); ?></div>
                                        <small class="text-muted"><?php echo date('h:i A', strtotime($activity['created_at'])); ?></small>
                                    </div>
                                </div>

                                <div class="activity-stats">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="d-flex align-items-center mb-1">
                                                <i class="fas fa-clock text-info me-2"></i>
                                                <span class="fw-medium">Watch Time:</span>
                                                <span class="ms-2"><?php echo floor($activity['watch_duration'] / 60); ?>m <?php echo $activity['watch_duration'] % 60; ?>s</span>
                                            </div>
                                        </div>
                                        <?php if ($activity['last_position']): ?>
                                            <div class="col-md-6">
                                                <div class="d-flex align-items-center mb-1">
                                                    <i class="fas fa-map-marker-alt text-warning me-2"></i>
                                                    <span class="fw-medium">Position:</span>
                                                    <span class="ms-2"><?php echo floor($activity['last_position'] / 60); ?>:<?php echo str_pad($activity['last_position'] % 60, 2, '0', STR_PAD_LEFT); ?></span>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    <?php elseif ($userId > 0 || $courseId > 0): ?>
        <div class="analytics-card text-center py-5">
            <div class="mb-3">
                <i class="fas fa-exclamation-triangle fa-3x text-warning"></i>
            </div>
            <h5 class="text-warning">Incomplete Selection</h5>
            <p class="text-muted">Please select both a user and a course to view detailed analytics.</p>
        </div>
    <?php else: ?>
        <div class="analytics-card text-center py-5">
            <div class="mb-4">
                <i class="fas fa-chart-line fa-4x text-muted opacity-50"></i>
            </div>
            <h4 class="text-muted mb-3">Welcome to Video Analytics</h4>
            <p class="text-muted mb-4">Get detailed insights into user video engagement and learning progress.</p>
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="feature-item">
                                <i class="fas fa-eye fa-2x text-primary mb-2"></i>
                                <h6>View Tracking</h6>
                                <small class="text-muted">Monitor video views and engagement</small>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="feature-item">
                                <i class="fas fa-chart-pie fa-2x text-success mb-2"></i>
                                <h6>Progress Analytics</h6>
                                <small class="text-muted">Track completion rates and progress</small>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="feature-item">
                                <i class="fas fa-clock fa-2x text-info mb-2"></i>
                                <h6>Time Analysis</h6>
                                <small class="text-muted">Analyze watch time and patterns</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Unlock Videos Modal -->
<?php if ($selectedCourse && $selectedUser): ?>
<div class="modal fade" id="unlockVideosModal" tabindex="-1" aria-labelledby="unlockVideosModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="unlockVideosModalLabel">Unlock Videos for <?php echo htmlspecialchars($selectedUser['name']); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p class="mb-4">Select which videos to unlock for this user. This will override the automatic weekly unlocking schedule.</p>

                <form action="bulk_unlock_videos.php" method="post" id="unlockVideosForm">
                    <input type="hidden" name="user_id" value="<?php echo $userId; ?>">
                    <input type="hidden" name="course_id" value="<?php echo $courseId; ?>">

                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th width="5%">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="selectAllVideos">
                                        </div>
                                    </th>
                                    <th width="10%">Week</th>
                                    <th width="55%">Video Title</th>
                                    <th width="15%">Duration</th>
                                    <th width="15%">Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($videos as $video): ?>
                                    <tr>
                                        <td>
                                            <div class="form-check">
                                                <input class="form-check-input video-checkbox" type="checkbox"
                                                       name="video_ids[]" value="<?php echo $video['id']; ?>"
                                                       <?php echo $video['is_unlocked'] ? 'checked disabled' : ''; ?>>
                                            </div>
                                        </td>
                                        <td>Week <?php echo $video['week_number']; ?></td>
                                        <td><?php echo htmlspecialchars($video['title']); ?></td>
                                        <td><?php echo $video['duration_minutes'] ? $video['duration_minutes'].' min' : 'N/A'; ?></td>
                                        <td>
                                            <?php if (!$video['is_unlocked']): ?>
                                                <span class="badge bg-secondary">Locked</span>
                                            <?php elseif ($video['is_completed']): ?>
                                                <span class="badge bg-success">Completed</span>
                                            <?php else: ?>
                                                <span class="badge bg-warning">In Progress</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="unlockVideosForm" class="btn btn-success">
                    <i class="fas fa-unlock me-1"></i> Unlock Selected Videos
                </button>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<style>
/* Additional modern styles */
.stat-mini {
    text-align: center;
}

.feature-item {
    text-align: center;
    padding: 1rem;
}

.info-item {
    padding: 0.5rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
    border-bottom: none;
}

.progress-ring {
    transition: stroke-dashoffset 0.5s ease-in-out;
}

.activity-timeline {
    max-height: 600px;
    overflow-y: auto;
}

.activity-timeline::-webkit-scrollbar {
    width: 6px;
}

.activity-timeline::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.activity-timeline::-webkit-scrollbar-thumb {
    background: #667eea;
    border-radius: 10px;
}

.activity-timeline::-webkit-scrollbar-thumb:hover {
    background: #5a6fd8;
}

@media (max-width: 768px) {
    .analytics-header {
        padding: 1.5rem 0;
        margin-bottom: 1.5rem;
    }

    .analytics-header h1 {
        font-size: 2rem;
    }

    .stats-container {
        margin-top: -1rem;
    }

    .stat-card {
        padding: 1.25rem;
    }

    .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .stat-content h3 {
        font-size: 1.25rem;
    }

    .timeline-item-modern {
        flex-direction: column;
        text-align: center;
    }

    .activity-icon {
        margin-bottom: 1rem;
    }
}
</style>



<?php require_once 'includes/footer.php'; ?>

<script>
$(document).ready(function() {
    // Animate cards on load
    $('.analytics-card, .stat-card, .user-selector, .timeline-modern, .video-progress-modern').each(function(index) {
        $(this).css('opacity', '0').css('transform', 'translateY(20px)');
        $(this).delay(index * 100).animate({
            opacity: 1
        }, 500).css('transform', 'translateY(0px)');
    });

    // Smooth scroll for internal links
    $('a[href^="#"]').on('click', function(event) {
        var target = $(this.getAttribute('href'));
        if (target.length) {
            event.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 100
            }, 1000);
        }
    });

    // Enhanced form validation
    $('form').on('submit', function(e) {
        var userSelect = $(this).find('select[name="user_id"]');
        var courseSelect = $(this).find('select[name="course_id"]');

        if (userSelect.length && !userSelect.val()) {
            e.preventDefault();
            userSelect.focus();
            showToast('Please select a user', 'warning');
            return false;
        }

        if (courseSelect.length && !courseSelect.val()) {
            e.preventDefault();
            courseSelect.focus();
            showToast('Please select a course', 'warning');
            return false;
        }
    });

    // Add loading state to buttons
    $('.btn-modern').on('click', function() {
        var $btn = $(this);
        if ($btn.attr('type') === 'submit') {
            $btn.prop('disabled', true);
            var originalText = $btn.html();
            $btn.html('<i class="fas fa-spinner fa-spin me-2"></i>Loading...');

            setTimeout(function() {
                $btn.prop('disabled', false);
                $btn.html(originalText);
            }, 3000);
        }
    });

    // Select all videos checkbox (for modal)
    $('#selectAllVideos').on('change', function() {
        $('.video-checkbox:not(:disabled)').prop('checked', $(this).prop('checked'));
    });

    // Update select all checkbox state when individual checkboxes change
    $('.video-checkbox').on('change', function() {
        var allChecked = $('.video-checkbox:not(:disabled)').length === $('.video-checkbox:not(:disabled):checked').length;
        $('#selectAllVideos').prop('checked', allChecked);
    });

    // Toast notification function
    function showToast(message, type = 'info') {
        var toastHtml = `
            <div class="toast align-items-center text-white bg-${type} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body">
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `;

        if (!$('.toast-container').length) {
            $('body').append('<div class="toast-container position-fixed top-0 end-0 p-3"></div>');
        }

        $('.toast-container').append(toastHtml);
        $('.toast').last().toast('show');
    }
});
</script>
