<?php
require_once 'includes/header.php';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Get current admin role and ID
$currentAdminRole = $auth->getUserRole();
$currentAdminId = $auth->getUserId();
$isSuperAdmin = $auth->hasRole('super_admin');
$isAdmin = $auth->hasRole('admin') || $isSuperAdmin;
$isStaff = $auth->hasRole('staff');

// Add premium styling for user_view.php with cache busting
echo '<link rel="stylesheet" href="assets/css/user-view-premium.css?v=' . time() . '">';

// Add inline styles to ensure premium styling is visible
echo '<style>
/* Force premium styling to be visible */
body {
    background-color: #23272b !important;
    color: #f8f9fa !important;
}

.profile-header-premium {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%) !important;
    color: white !important;
    padding: 2rem !important;
    border-radius: 0.5rem !important;
    margin-bottom: 1.5rem !important;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
}

.profile-avatar-premium {
    width: 100px !important;
    height: 100px !important;
    border-radius: 50% !important;
    border: 4px solid rgba(255, 255, 255, 0.3) !important;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.2) !important;
}

.profile-name-premium {
    font-size: 2rem !important;
    font-weight: 700 !important;
    margin-bottom: 0.25rem !important;
    color: white !important;
}

.badge-premium {
    background: rgba(255, 255, 255, 0.2) !important;
    color: white !important;
    padding: 0.35rem 0.75rem !important;
    border-radius: 50px !important;
    font-size: 0.8rem !important;
    font-weight: 500 !important;
}

.nav-tabs-premium {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1) !important;
    padding: 0 1rem !important;
}

.nav-link-premium {
    padding: 1rem 1.25rem !important;
    color: #6c757d !important;
    font-weight: 500 !important;
    border: none !important;
    background: transparent !important;
    transition: all 0.2s ease-in-out !important;
}

.nav-link-premium.active {
    color: #4e73df !important;
    font-weight: 600 !important;
}

.nav-link-premium.active::after {
    content: "" !important;
    position: absolute !important;
    bottom: -1px !important;
    left: 0 !important;
    width: 100% !important;
    height: 3px !important;
    background: #4e73df !important;
    border-radius: 3px 3px 0 0 !important;
}

.premium-card {
    border-radius: 0.5rem !important;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
    transition: all 0.2s ease-in-out !important;
    border: none !important;
}

.premium-card:hover {
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
    transform: translateY(-2px) !important;
}

.user-profile-card-premium {
    border-radius: 0.5rem !important;
    overflow: hidden !important;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
}

.profile-header-premium-card {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%) !important;
    color: white !important;
    padding: 2rem 1.5rem !important;
}

.user-avatar-premium-card {
    width: 120px !important;
    height: 120px !important;
    border-radius: 50% !important;
    border: 4px solid rgba(255, 255, 255, 0.3) !important;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.2) !important;
    margin: 0 auto !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 3rem !important;
    font-weight: 700 !important;
    color: white !important;
}

.badge-premium-card {
    background: rgba(255, 255, 255, 0.2) !important;
    color: white !important;
    padding: 0.35rem 0.75rem !important;
    border-radius: 50px !important;
    font-size: 0.8rem !important;
    font-weight: 500 !important;
}

.user-info-item-premium {
    display: flex !important;
    align-items: center !important;
    margin-bottom: 1.25rem !important;
}

.user-info-icon-premium {
    width: 40px !important;
    height: 40px !important;
    border-radius: 50% !important;
    background: #f8f9fc !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: #4e73df !important;
    margin-right: 1rem !important;
}

.user-info-label-premium {
    font-size: 0.8rem !important;
    color: #6c757d !important;
    margin-bottom: 0.25rem !important;
}

.user-info-value-premium {
    font-weight: 500 !important;
}
</style>';

// Check if user has permission to access this page
if (!$isAdmin && !$isStaff && !$auth->hasPermission('view_users')) {
    Utilities::setFlashMessage('error', 'You do not have permission to view user details.');
    Utilities::redirect('index.php');
}

// Check if user ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    Utilities::setFlashMessage('error', 'Invalid user ID.');
    Utilities::redirect('users.php');
}

$userId = (int)$_GET['id'];

// Handle device reset
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['reset_device']) && $_POST['reset_device'] == '1') {
    $resetQuery = "UPDATE users SET device_id = NULL WHERE id = ?";
    $resetStmt = $conn->prepare($resetQuery);
    $resetStmt->bind_param("i", $userId);
    if ($resetStmt->execute()) {
        Utilities::setFlashMessage('success', 'Device registration has been reset. The user will need to re-register their device.');
    } else {
        Utilities::setFlashMessage('error', 'Failed to reset device registration.');
    }
    Utilities::redirect('user_view.php?id=' . $userId);
}

// Handle device revocation via AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'revoke_device') {
    header('Content-Type: application/json');

    try {
        // Validate required fields
        if (!isset($_POST['device_id']) || !isset($_POST['reason'])) {
            throw new Exception('Missing required fields');
        }

        $deviceId = trim($_POST['device_id']);
        $reason = trim($_POST['reason']);
        $adminUserId = $currentAdminId;
        $adminUsername = $_SESSION['username'] ?? $_SESSION['name'] ?? 'Unknown Admin';

        // Get client information for logging
        $ipAddress = $_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';

        // Start transaction
        $conn->begin_transaction();

        try {
            // 1. Invalidate all API tokens for this user and device
            $revokeTokensStmt = $conn->prepare("
                UPDATE api_tokens
                SET is_revoked = 1, revoked_by_admin = ?, revoked_at = NOW()
                WHERE user_id = ? AND (device_id = ? OR device_id IS NULL)
            ");
            $revokeTokensStmt->bind_param("iis", $adminUserId, $userId, $deviceId);
            $revokeTokensStmt->execute();
            $revokedTokensCount = $revokeTokensStmt->affected_rows;
            $revokeTokensStmt->close();

            // 2. Update device session as revoked
            $revokeSessionStmt = $conn->prepare("
                UPDATE device_sessions
                SET is_active = 0, revoked_by_admin = ?, revoked_at = NOW(), revocation_reason = ?
                WHERE user_id = ? AND device_id = ?
            ");
            $revokeSessionStmt->bind_param("isis", $adminUserId, $reason, $userId, $deviceId);
            $revokeSessionStmt->execute();
            $revokedSessionsCount = $revokeSessionStmt->affected_rows;
            $revokeSessionStmt->close();

            // 3. Clear device ID from user record to force re-authentication
            $clearDeviceStmt = $conn->prepare("UPDATE users SET device_id = NULL WHERE id = ?");
            $clearDeviceStmt->bind_param("i", $userId);
            $clearDeviceStmt->execute();
            $clearDeviceStmt->close();

            // 4. Log admin action
            $actionDetails = json_encode([
                'revoked_tokens' => $revokedTokensCount,
                'revoked_sessions' => $revokedSessionsCount,
                'original_device_id' => $deviceId,
                'timestamp' => date('Y-m-d H:i:s')
            ]);

            $logActionStmt = $conn->prepare("
                INSERT INTO admin_action_logs
                (admin_user_id, admin_username, action_type, target_user_id, target_username,
                 target_device_id, action_details, reason, ip_address, user_agent)
                VALUES (?, ?, 'device_revoke', ?, ?, ?, ?, ?, ?, ?)
            ");
            $logActionStmt->bind_param(
                "ississsss",
                $adminUserId,
                $adminUsername,
                $userId,
                $user['username'] ?? $user['name'],
                $deviceId,
                $actionDetails,
                $reason,
                $ipAddress,
                $userAgent
            );
            $logActionStmt->execute();
            $logActionStmt->close();

            // Commit transaction
            $conn->commit();

            // Log the action for monitoring
            error_log("ADMIN ACTION: Device revoked by {$adminUsername} (ID: {$adminUserId}) for user {$user['name']} (ID: {$userId}). Device: {$deviceId}. Reason: {$reason}");

            echo json_encode([
                'success' => true,
                'message' => 'Device access revoked successfully',
                'details' => [
                    'revoked_tokens' => $revokedTokensCount,
                    'revoked_sessions' => $revokedSessionsCount,
                    'revoked_at' => date('Y-m-d H:i:s')
                ]
            ]);
            exit;

        } catch (Exception $e) {
            $conn->rollback();
            throw $e;
        }

    } catch (Exception $e) {
        error_log("Device revocation error in user_view.php: " . $e->getMessage());
        echo json_encode([
            'success' => false,
            'error' => 'Failed to revoke device access: ' . $e->getMessage()
        ]);
        exit;
    }
}



// Handle staff reassignment (for super admins only)
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['reassign_staff']) && $isSuperAdmin) {
    $newStaffId = isset($_POST['new_staff_id']) && !empty($_POST['new_staff_id']) ? (int)$_POST['new_staff_id'] : null;

    $updateQuery = "UPDATE users SET assigned_staff_id = ? WHERE id = ?";
    $updateStmt = $conn->prepare($updateQuery);
    $updateStmt->bind_param("ii", $newStaffId, $userId);

    if ($updateStmt->execute()) {
        // Get staff name for the message
        $staffName = "No staff";
        if ($newStaffId) {
            $staffQuery = "SELECT name FROM admin_users WHERE id = ?";
            $staffStmt = $conn->prepare($staffQuery);
            $staffStmt->bind_param("i", $newStaffId);
            $staffStmt->execute();
            $staffResult = $staffStmt->get_result();
            if ($staffResult->num_rows > 0) {
                $staffName = $staffResult->fetch_assoc()['name'];
            }
        }

        Utilities::setFlashMessage('success', 'User has been reassigned to ' . htmlspecialchars($staffName) . '.');

        // Log the activity
        if (function_exists('logStaffActivity')) {
            $details = "Reassigned user {$user['name']} to staff member " . ($newStaffId ? $staffName : "None");
            logStaffActivity($conn, $currentAdminId, 'user_reassign', $userId, $details);
        }
    } else {
        Utilities::setFlashMessage('error', 'Failed to reassign user.');
    }

    Utilities::redirect('user_view.php?id=' . $userId);
}

// Get all staff members for reassignment (for super admins)
$staffMembers = [];
if ($isSuperAdmin) {
    $staffQuery = "SELECT id, name, username FROM admin_users WHERE role = 'staff' AND is_active = 1 ORDER BY name";
    $staffResult = $conn->query($staffQuery);
    if ($staffResult && $staffResult->num_rows > 0) {
        while ($staff = $staffResult->fetch_assoc()) {
            $staffMembers[] = $staff;
        }
    }
}

// Get user data with device session information
$userQuery = "
    SELECT u.*,
           ds.id as device_session_id,
           ds.device_info,
           ds.first_login as device_first_login,
           ds.last_activity as device_last_activity,
           ds.is_active as device_is_active,
           ds.revoked_by_admin,
           ds.revoked_at,
           ds.revocation_reason,
           au.username as revoked_by_username,
           (SELECT COUNT(*) FROM api_tokens WHERE user_id = u.id AND device_id = u.device_id AND is_revoked = 0) as active_tokens
    FROM users u
    LEFT JOIN device_sessions ds ON u.id = ds.user_id AND u.device_id = ds.device_id
    LEFT JOIN admin_users au ON ds.revoked_by_admin = au.id
    WHERE u.id = ?
";
$stmt = $conn->prepare($userQuery);
$stmt->bind_param("i", $userId);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    Utilities::setFlashMessage('error', 'User not found.');
    Utilities::redirect('users.php');
}

$user = $result->fetch_assoc();

// Get current assigned staff info
$assignedStaffInfo = null;
if (!empty($user['assigned_staff_id'])) {
    $assignedStaffQuery = "SELECT id, name, username FROM admin_users WHERE id = ?";
    $assignedStaffStmt = $conn->prepare($assignedStaffQuery);
    $assignedStaffStmt->bind_param("i", $user['assigned_staff_id']);
    $assignedStaffStmt->execute();
    $assignedStaffResult = $assignedStaffStmt->get_result();
    if ($assignedStaffResult->num_rows > 0) {
        $assignedStaffInfo = $assignedStaffResult->fetch_assoc();
    }
}

// Check if staff member is trying to access a user not assigned to them
if ($isStaff && !$isSuperAdmin) {
    if ($user['assigned_staff_id'] != $currentAdminId) {
        Utilities::setFlashMessage('error', 'You do not have permission to view this user\'s details. This user is not assigned to you.');
        Utilities::redirect('users.php');
    }
}

// Get user's session access
$sessionAccess = [];

// Check if session_access table exists
$tableExistsQuery = "SHOW TABLES LIKE 'session_access'";
$tableExistsResult = $conn->query($tableExistsQuery);
$sessionAccessTableExists = ($tableExistsResult->num_rows > 0);

if ($sessionAccessTableExists) {
    $accessQuery = "SELECT session_type, access_level FROM session_access WHERE user_id = ?";
    $accessStmt = $conn->prepare($accessQuery);
    $accessStmt->bind_param("i", $userId);
    $accessStmt->execute();
    $accessResult = $accessStmt->get_result();

    while ($access = $accessResult->fetch_assoc()) {
        $sessionAccess[$access['session_type']] = $access['access_level'];
    }
}

// Get user's course enrollments
$enrollmentsQuery = "SELECT c.id, c.title, c.category, uce.status, uce.start_date, uce.end_date,
                    (SELECT COUNT(*) FROM course_videos WHERE course_id = c.id) as total_videos,
                    (SELECT COUNT(*) FROM user_video_progress WHERE user_id = ? AND video_id IN
                        (SELECT id FROM course_videos WHERE course_id = c.id) AND is_completed = 1) as completed_videos
                    FROM user_course_enrollments uce
                    JOIN courses c ON uce.course_id = c.id
                    WHERE uce.user_id = ?
                    ORDER BY uce.start_date DESC";
$enrollmentsStmt = $conn->prepare($enrollmentsQuery);
$enrollmentsStmt->bind_param("ii", $userId, $userId);
$enrollmentsStmt->execute();
$enrollmentsResult = $enrollmentsStmt->get_result();

// Get user's workout history
$workoutsQuery = "SELECT * FROM workout_history WHERE user_id = ? ORDER BY date DESC LIMIT 10";
$workoutsStmt = $conn->prepare($workoutsQuery);
$workoutsStmt->bind_param("i", $userId);
$workoutsStmt->execute();
$workoutsResult = $workoutsStmt->get_result();

// Calculate BMI if height and weight are available
$bmi = null;
$bmiCategory = '';
if (!empty($user['height']) && !empty($user['weight'])) {
    $heightInMeters = $user['height'] / 100;
    $bmi = $user['weight'] / ($heightInMeters * $heightInMeters);

    // Determine BMI category
    if ($bmi < 18.5) {
        $bmiCategory = 'Underweight';
        $bmiColor = 'text-warning';
    } elseif ($bmi < 25) {
        $bmiCategory = 'Normal weight';
        $bmiColor = 'text-success';
    } elseif ($bmi < 30) {
        $bmiCategory = 'Overweight';
        $bmiColor = 'text-warning';
    } else {
        $bmiCategory = 'Obese';
        $bmiColor = 'text-danger';
    }
}





// Get device revocation history
$deviceRevocationHistory = [];
$revocationHistoryQuery = "
    SELECT aal.*, au.name as admin_name
    FROM admin_action_logs aal
    LEFT JOIN admin_users au ON aal.admin_user_id = au.id
    WHERE aal.target_user_id = ? AND aal.action_type = 'device_revoke'
    ORDER BY aal.created_at DESC
    LIMIT 10
";
$revocationHistoryStmt = $conn->prepare($revocationHistoryQuery);
$revocationHistoryStmt->bind_param("i", $userId);
$revocationHistoryStmt->execute();
$revocationHistoryResult = $revocationHistoryStmt->get_result();
while ($row = $revocationHistoryResult->fetch_assoc()) {
    $deviceRevocationHistory[] = $row;
}

// Get activity timeline for Recent Workouts section
$timelineQuery = "SELECT
                 al.*,
                 cv.title as video_title,
                 cv.week_number,
                 al.details,
                 CASE
                     WHEN al.details LIKE '%watch_duration%' THEN
                         CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(al.details, '\"watch_duration\":', -1), ',', 1) AS UNSIGNED)
                     ELSE 0
                 END as watch_duration,
                 CASE
                     WHEN al.details LIKE '%last_position%' THEN
                         CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(al.details, '\"last_position\":', -1), ',', 1) AS UNSIGNED)
                     ELSE 0
                 END as last_position,
                 CASE
                     WHEN al.details LIKE '%\"is_completed\":true%' OR al.details LIKE '%\"is_completed\":1%' THEN 1
                     ELSE 0
                 END as is_completed,
                 CASE
                     WHEN al.details LIKE '%\"action\":\"%' THEN
                         SUBSTRING_INDEX(SUBSTRING_INDEX(al.details, '\"action\":\"', -1), '\"', 1)
                     ELSE 'unknown'
                 END as action
                 FROM user_activity_log al
                 JOIN course_videos cv ON al.related_id = cv.id
                 WHERE al.user_id = ? AND al.activity_type = 'video_progress'
                 ORDER BY al.created_at DESC
                 LIMIT 4";
$timelineStmt = $conn->prepare($timelineQuery);
$timelineStmt->bind_param("i", $userId);
$timelineStmt->execute();
$timelineResult = $timelineStmt->get_result();
$recentWorkouts = [];

while ($activity = $timelineResult->fetch_assoc()) {
    $recentWorkouts[] = $activity;
}
?>

<div class="user-profile-header mb-4">
    <div class="container-fluid px-0">
        <div class="row g-0">
            <div class="col-12">
                <!-- Premium Profile Header with Gradient Background -->
                <div class="profile-header-premium">
                    <div class="profile-header-content">
                        <div class="d-flex align-items-center">
                            <div class="profile-avatar-premium me-4">
                                <?php
                                $profileImageUrl = $user['profile_image_url'] ?? '';

                                if (!empty($profileImageUrl)) {
                                    // Check if the image URL is a relative path
                                    if (!preg_match('/^https?:\/\//', $profileImageUrl)) {
                                        // Convert relative path to absolute URL
                                        $baseUrl = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
                                        $baseUrl .= $_SERVER['HTTP_HOST'];

                                        // Remove leading slash if present
                                        if (substr($profileImageUrl, 0, 1) === '/') {
                                            $profileImageUrl = substr($profileImageUrl, 1);
                                        }

                                        $profileImageUrl = "$baseUrl/$profileImageUrl";
                                    }

                                    // Add cache busting parameter
                                    $profileImageUrl .= (strpos($profileImageUrl, '?') !== false ? '&' : '?') . 't=' . time();
                                ?>
                                    <img src="<?php echo htmlspecialchars($profileImageUrl); ?>" alt="<?php echo htmlspecialchars($user['name']); ?>" class="profile-img-premium">
                                <?php } else { ?>
                                    <div class="profile-initials-premium" style="background-color: <?php echo Utilities::getColorFromName($user['name']); ?>;">
                                        <?php echo strtoupper(substr($user['name'], 0, 1)); ?>
                                    </div>
                                <?php } ?>

                                <?php if ($user['is_active']): ?>
                                    <span class="status-badge-premium active" title="Active User"></span>
                                <?php else: ?>
                                    <span class="status-badge-premium inactive" title="Inactive User"></span>
                                <?php endif; ?>
                            </div>

                            <div class="profile-info-premium">
                                <h1 class="profile-name-premium"><?php echo htmlspecialchars($user['name']); ?></h1>
                                <div class="profile-meta-premium">
                                    <span class="profile-username-premium">@<?php echo htmlspecialchars($user['username']); ?></span>
                                    <span class="profile-divider">•</span>
                                    <span class="profile-joined">
                                        <i class="fas fa-calendar-alt me-1"></i> Joined <?php echo !empty($user['created_at']) ? Utilities::formatDate($user['created_at'], 'M d, Y') : 'N/A'; ?>
                                    </span>
                                    <span class="profile-divider">•</span>
                                    <span class="profile-last-login" title="<?php echo !empty($user['last_login']) ? Utilities::formatDate($user['last_login'], 'M d, Y h:i A') : 'Never'; ?>">
                                        <i class="fas fa-sign-in-alt me-1"></i> Last login <?php echo !empty($user['last_login']) ? Utilities::formatDate($user['last_login'], 'M d, Y h:i A') : 'Never'; ?>
                                    </span>
                                </div>

                                <div class="profile-badges-premium mt-2">
                                    <span class="badge-premium">
                                        <i class="fas fa-user me-1"></i> KFT User
                                    </span>

                                    <?php if ($assignedStaffInfo): ?>
                                    <span class="badge-premium info">
                                        <i class="fas fa-user-shield me-1"></i> Assigned to <?php echo htmlspecialchars($assignedStaffInfo['name']); ?>
                                    </span>
                                    <?php endif; ?>

                                    <?php if (!empty($user['device_id'])): ?>
                                    <span class="badge-premium success">
                                        <i class="fas fa-mobile-alt me-1"></i> Device Registered
                                    </span>
                                    <?php else: ?>
                                    <span class="badge-premium warning">
                                        <i class="fas fa-mobile-alt me-1"></i> No Device
                                    </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="profile-header-actions-premium">
                        <div class="btn-group">
                            <a href="users.php" class="btn btn-light btn-premium">
                                <i class="fas fa-arrow-left me-2"></i> Back to Users
                            </a>
                            <a href="user_edit.php?id=<?php echo $userId; ?>" class="btn btn-primary btn-premium">
                                <i class="fas fa-edit me-2"></i> Edit User
                            </a>
                            <button type="button" class="btn btn-light btn-premium dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false">
                                <span class="visually-hidden">Toggle Dropdown</span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end shadow-sm">
                                <?php if ($userId != 1): ?>
                                <li>
                                    <a class="dropdown-item" href="users.php?<?php echo $user['is_active'] ? 'action=deactivate' : 'action=activate'; ?>&id=<?php echo $userId; ?>">
                                        <i class="fas fa-<?php echo $user['is_active'] ? 'toggle-off text-warning' : 'toggle-on text-success'; ?> me-2"></i>
                                        <?php echo $user['is_active'] ? 'Deactivate User' : 'Activate User'; ?>
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item text-danger" href="users.php?delete=<?php echo $userId; ?>" onclick="return confirm('Are you sure you want to delete this user? This action cannot be undone.')">
                                        <i class="fas fa-trash-alt me-2"></i> Delete User
                                    </a>
                                </li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                </div>


            </div>
        </div>
    </div>
</div>

<?php Utilities::displayFlashMessages(); ?>

<div class="row">
    <!-- Priority 1: User Profile Card - Most Important -->
    <div class="col-lg-4 col-md-12 mb-3">
        <div class="card border-0 shadow-sm user-profile-card-premium">
            <div class="card-body p-0">
                <div class="profile-header-premium-card p-4 text-center">
                    <div class="position-relative mb-4">
                        <?php
                        $profileImageUrl = $user['profile_image_url'] ?? '';

                        if (!empty($profileImageUrl)) {
                            // Check if the image URL is a relative path
                            if (!preg_match('/^https?:\/\//', $profileImageUrl)) {
                                // Convert relative path to absolute URL
                                $baseUrl = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
                                $baseUrl .= $_SERVER['HTTP_HOST'];

                                // Remove leading slash if present
                                if (substr($profileImageUrl, 0, 1) === '/') {
                                    $profileImageUrl = substr($profileImageUrl, 1);
                                }

                                $profileImageUrl = "$baseUrl/$profileImageUrl";
                            }

                            // Add cache busting parameter
                            $profileImageUrl .= (strpos($profileImageUrl, '?') !== false ? '&' : '?') . 't=' . time();
                        ?>
                            <div class="user-avatar-premium-card mx-auto">
                                <img src="<?php echo htmlspecialchars($profileImageUrl); ?>" alt="<?php echo htmlspecialchars($user['name']); ?>" style="width:100%; height:100%; object-fit:cover; border-radius:50%;">
                            </div>
                        <?php } else { ?>
                            <div class="user-avatar-premium-card mx-auto" style="background-color: <?php echo Utilities::getColorFromName($user['name']); ?>;">
                                <?php echo strtoupper(substr($user['name'], 0, 1)); ?>
                            </div>
                        <?php } ?>

                        <?php if ($user['is_active']): ?>
                            <span class="status-indicator-premium active" title="Active User"></span>
                        <?php else: ?>
                            <span class="status-indicator-premium inactive" title="Inactive User"></span>
                        <?php endif; ?>
                    </div>
                    <h4 class="mb-1 fw-bold"><?php echo htmlspecialchars($user['name']); ?></h4>
                    <p class="text-muted mb-2">@<?php echo htmlspecialchars($user['username']); ?></p>

                    <div class="d-flex justify-content-center mb-3">
                        <span class="badge-premium-card">
                            <i class="fas fa-user me-1"></i> KFT User
                        </span>
                    </div>
                </div>

                <div class="profile-details-premium p-4">
                    <div class="user-info-item-premium">
                        <div class="user-info-icon-premium">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div class="user-info-content-premium">
                            <div class="user-info-label-premium">Phone</div>
                            <div class="user-info-value-premium"><?php echo htmlspecialchars($user['phone_number'] ?? 'Not provided'); ?></div>
                        </div>
                    </div>

                    <div class="user-info-item-premium">
                        <div class="user-info-icon-premium">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="user-info-content-premium">
                            <div class="user-info-label-premium">Email</div>
                            <div class="user-info-value-premium"><?php echo htmlspecialchars($user['email'] ?? 'Not provided'); ?></div>
                        </div>
                    </div>

                    <div class="user-info-item-premium">
                        <div class="user-info-icon-premium">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div class="user-info-content-premium">
                            <div class="user-info-label-premium">Joined</div>
                            <div class="user-info-value-premium"><?php echo !empty($user['created_at']) ? Utilities::formatDate($user['created_at']) : ''; ?></div>
                        </div>
                    </div>

                    <div class="user-info-item-premium">
                        <div class="user-info-icon-premium">
                            <i class="fas fa-sign-in-alt"></i>
                        </div>
                        <div class="user-info-content-premium">
                            <div class="user-info-label-premium">Last Login</div>
                            <div class="user-info-value-premium">
                                <?php if (!empty($user['last_login'])): ?>
                                    <span><?php echo Utilities::formatDate($user['last_login'], 'M d, Y'); ?></span>
                                    <small class="text-muted d-block"><?php echo Utilities::formatDate($user['last_login'], 'h:i A'); ?></small>
                                <?php else: ?>
                                    <span class="text-muted">Never</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced PIN Management Section -->
                    <div class="pin-management-section-premium">
                      <div class="pin-header-premium">
                        <div class="pin-icon-container-premium">
                          <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="pin-title-section-premium">
                          <h4 class="pin-title-premium">App Login PIN</h4>
                          <p class="pin-subtitle-premium">Secure 4-digit code for mobile app access</p>
                        </div>
                        <div class="pin-security-badge-premium">
                          <i class="fas fa-lock"></i>
                          <span>Encrypted</span>
                        </div>
                      </div>
                      <div class="pin-content-premium">
                        <?php if (!empty($user['pin'])): ?>
                          <div class="pin-status-card-premium pin-active-premium">
                            <div class="d-flex flex-wrap align-items-center justify-content-between gap-3">
                              <div class="pin-value-container-premium flex-grow-1" style="min-width:120px; max-width:180px; overflow-x:auto;">
                                <div class="pin-dots-premium" id="pin-dots-display">
                                  <span class="pin-dot-premium"></span>
                                  <span class="pin-dot-premium"></span>
                                  <span class="pin-dot-premium"></span>
                                  <span class="pin-dot-premium"></span>
                                </div>
                                <div class="pin-actual-value-premium" id="pin-actual-value" style="display: none;"><?php echo htmlspecialchars($user['pin']); ?></div>
                              </div>
                              <div class="pin-actions-premium d-flex flex-column flex-md-row gap-2" style="min-width:90px;">
                                <button type="button" class="pin-action-btn-premium pin-copy-btn-premium"
                                  onclick="copyPinToClipboard('<?php echo htmlspecialchars($user['pin']); ?>', this)"
                                  data-bs-toggle="tooltip"
                                  data-bs-placement="top"
                                  title="Copy PIN to clipboard"
                                  aria-label="Copy PIN to clipboard">
                                  <i class="fas fa-copy"></i>
                                  <span class="btn-text-premium">Copy</span>
                                </button>
                                <button type="button" class="pin-action-btn-premium pin-reveal-btn-premium"
                                  onclick="togglePinVisibility(this)"
                                  data-bs-toggle="tooltip"
                                  data-bs-placement="top"
                                  title="Show/Hide PIN"
                                  aria-label="Toggle PIN visibility">
                                  <i class="fas fa-eye"></i>
                                  <span class="btn-text-premium">Show</span>
                                </button>
                                <button type="button" class="pin-action-btn-premium pin-change-btn-premium"
                                  data-bs-toggle="modal"
                                  data-bs-target="#changePinModal"
                                  data-bs-toggle="tooltip"
                                  data-bs-placement="top"
                                  title="Change PIN"
                                  aria-label="Change PIN">
                                  <i class="fas fa-edit"></i>
                                  <span class="btn-text-premium">Change</span>
                                </button>
                              </div>
                            </div>
                            <div class="pin-expiry-info-premium mt-2">
                              <?php if (!empty($user['pin_expires_at']) && strtotime($user['pin_expires_at']) > time()): ?>
                                <i class="fas fa-clock"></i>
                                <span>Expires <?php echo date('M d, Y h:i A', strtotime($user['pin_expires_at'])); ?></span>
                              <?php else: ?>
                                <i class="fas fa-exclamation-triangle text-warning"></i>
                                <span class="text-warning">PIN Expired</span>
                              <?php endif; ?>
                            </div>
                            <div class="pin-security-notice-premium mt-2">
                              <i class="fas fa-info-circle"></i>
                              <span>This PIN is encrypted and securely stored. Only share it directly with the user.</span>
                            </div>
                          </div>
                        <?php else: ?>
                          <!-- PIN Not Set State (keep as is, but ensure responsive) -->
                          <div class="pin-status-card-premium pin-inactive-premium">
                            <div class="pin-empty-state-premium">
                              <div class="pin-empty-icon-premium">
                                <i class="fas fa-key-skeleton"></i>
                              </div>
                              <div class="pin-empty-content-premium">
                                <h5 class="pin-empty-title-premium">No PIN Set</h5>
                                <p class="pin-empty-description-premium">User needs a 4-digit PIN to access the mobile app</p>
                              </div>
                            </div>
                            <div class="pin-setup-action-premium">
                              <button type="button" class="pin-setup-btn-premium"
                                data-bs-toggle="modal"
                                data-bs-target="#changePinModal"
                                aria-label="Set up new PIN">
                                <i class="fas fa-plus-circle"></i>
                                <span>Set Up PIN</span>
                              </button>
                            </div>
                            <div class="pin-security-notice-premium">
                              <i class="fas fa-shield-alt"></i>
                              <span>PIN will be automatically generated and encrypted for security</span>
                            </div>
                          </div>
                        <?php endif; ?>
                      </div>
                    </div>
                    <style>
                    @media (max-width: 600px) {
                      .pin-management-section-premium,
                      .pin-status-card-premium {
                        padding: 10px !important;
                      }
                      .pin-actions-premium {
                        flex-direction: column !important;
                        gap: 8px !important;
                        width: 100%;
                      }
                      .pin-value-container-premium {
                        min-width: 100px !important;
                        max-width: 100% !important;
                      }
                    }
                    .pin-value-container-premium {
                      min-width: 120px;
                      max-width: 180px;
                      overflow-x: auto;
                    }
                    .pin-actions-premium {
                      display: flex;
                      flex-direction: row;
                      gap: 12px;
                    }
                    </style>
                </div>
            </div>
            <div class="card-footer-premium py-3">
                <div class="d-flex justify-content-between">
                    <a href="user_edit.php?id=<?php echo $userId; ?>" class="btn btn-primary btn-premium-sm">
                        <i class="fas fa-edit me-1"></i> Edit Profile
                    </a>
                    <div class="dropdown">
                        <button class="btn btn-light btn-premium-sm dropdown-toggle" type="button" id="userActionsDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-cog me-1"></i> Actions
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end shadow-sm" aria-labelledby="userActionsDropdown">
                            <?php if ($userId != 1): ?>
                                <li>
                                    <a class="dropdown-item" href="users.php?<?php echo $user['is_active'] ? 'action=deactivate' : 'action=activate'; ?>&id=<?php echo $userId; ?>">
                                        <i class="fas fa-<?php echo $user['is_active'] ? 'toggle-off text-warning' : 'toggle-on text-success'; ?> me-2"></i>
                                        <?php echo $user['is_active'] ? 'Deactivate User' : 'Activate User'; ?>
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item text-danger" href="users.php?delete=<?php echo $userId; ?>" onclick="return confirm('Are you sure you want to delete this user? This action cannot be undone.')">
                                        <i class="fas fa-trash-alt me-2"></i> Delete User
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <?php if ($isSuperAdmin): ?>
        <!-- Staff Assignment Card (Super Admin Only) - Compact Design -->
        <div class="card border-0 shadow-sm premium-card staff-assignment-card-compact mt-3">
            <div class="card-header-premium d-flex align-items-center">
                <div class="icon-circle-premium bg-primary text-white me-3">
                    <i class="fas fa-user-shield"></i>
                </div>
                <h6 class="m-0 font-weight-bold text-primary">Staff Assignment</h6>
            </div>
            <div class="card-body-premium">
                <div class="mb-3">
                    <div class="premium-label">Current Assignment</div>
                    <?php if ($assignedStaffInfo): ?>
                        <div class="d-flex align-items-center mt-2">
                            <div class="avatar-circle-premium bg-primary text-white me-2">
                                <?php echo strtoupper(substr($assignedStaffInfo['name'], 0, 1)); ?>
                            </div>
                            <div>
                                <div class="fw-bold"><?php echo htmlspecialchars($assignedStaffInfo['name']); ?></div>
                                <div class="small text-muted">@<?php echo htmlspecialchars($assignedStaffInfo['username']); ?></div>
                            </div>
                        </div>
                    <?php else: ?>
                        <span class="badge-premium secondary mt-2">Not assigned to any staff</span>
                    <?php endif; ?>
                </div>

                <form method="post" action="user_view.php?id=<?php echo $userId; ?>">
                    <input type="hidden" name="reassign_staff" value="1">
                    <div class="mb-3">
                        <label for="new_staff_id" class="form-label-premium">Reassign to:</label>
                        <select class="form-select-premium" id="new_staff_id" name="new_staff_id">
                            <option value="">No Staff Assignment</option>
                            <?php foreach ($staffMembers as $staff): ?>
                                <option value="<?php echo $staff['id']; ?>" <?php echo ($user['assigned_staff_id'] == $staff['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($staff['name'] . ' (' . $staff['username'] . ')'); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <button type="submit" class="btn btn-primary btn-premium-sm">
                        <i class="fas fa-user-tag me-1"></i> Reassign User
                    </button>
                </form>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- Priority 2: User Stats & Analytics - High Priority -->
    <div class="col-lg-8 col-md-12">
        <div class="row">
            <!-- Physical Stats - Premium Design -->
            <div class="col-lg-6 col-md-12 mb-3">
                <div class="card border-0 shadow-sm h-100 physical-stats-card-premium">
                    <div class="card-header-premium d-flex align-items-center">
                        <div class="icon-circle-premium bg-gradient-primary text-white me-3">
                            <i class="fas fa-heartbeat"></i>
                        </div>
                        <h6 class="m-0 font-weight-bold text-primary">Physical Stats</h6>
                    </div>

                    <div class="card-body-premium">
                        <?php if ($bmi): ?>
                        <!-- BMI Gauge - Premium Design -->
                        <div class="bmi-gauge-container-premium">
                            <div class="bmi-category-premium text-center mb-3">
                                <span class="bmi-badge-premium <?php
                                    if ($bmi < 18.5) echo 'info';
                                    else if ($bmi < 25) echo 'success';
                                    else if ($bmi < 30) echo 'warning';
                                    else echo 'danger';
                                ?>">
                                    <i class="fas fa-<?php
                                        if ($bmi < 18.5) echo 'info-circle';
                                        else if ($bmi < 25) echo 'check-circle';
                                        else if ($bmi < 30) echo 'exclamation-circle';
                                        else echo 'exclamation-triangle';
                                    ?> me-2"></i>
                                    BMI: <?php echo number_format($bmi, 1); ?> - <?php echo $bmiCategory; ?>
                                </span>
                            </div>
                            <div class="bmi-gauge-premium">
                                <div class="bmi-gauge-scale-premium">
                                    <div class="bmi-gauge-section-premium bmi-underweight-premium" data-tooltip="Underweight (<18.5)"></div>
                                    <div class="bmi-gauge-section-premium bmi-normal-premium" data-tooltip="Normal (18.5-24.9)"></div>
                                    <div class="bmi-gauge-section-premium bmi-overweight-premium" data-tooltip="Overweight (25-29.9)"></div>
                                    <div class="bmi-gauge-section-premium bmi-obese-premium" data-tooltip="Obese (≥30)"></div>
                                </div>
                                <div class="bmi-gauge-indicator-premium" style="left: <?php
                                    // Calculate position (0-100%)
                                    $position = 0;
                                    if ($bmi < 18.5) {
                                        $position = ($bmi / 18.5) * 25; // 0-25% of gauge
                                    } else if ($bmi < 25) {
                                        $position = 25 + (($bmi - 18.5) / 6.5) * 25; // 25-50% of gauge
                                    } else if ($bmi < 30) {
                                        $position = 50 + (($bmi - 25) / 5) * 25; // 50-75% of gauge
                                    } else {
                                        $position = 75 + min((($bmi - 30) / 10) * 25, 25); // 75-100% of gauge, capped at 100%
                                    }
                                    echo min(max($position, 0), 100) . '%';
                                ?>">
                                    <div class="bmi-gauge-indicator-value-premium"><?php echo number_format($bmi, 1); ?></div>
                                </div>
                                <div class="bmi-gauge-labels-premium">
                                    <span>16</span>
                                    <span>18.5</span>
                                    <span>25</span>
                                    <span>30</span>
                                    <span>40</span>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Physical Stats Grid - Premium Design -->
                        <div class="physical-stats-grid-premium">
                            <!-- Height & Weight Row -->
                            <div class="physical-stat-item-premium">
                                <div class="physical-stat-icon-premium">
                                    <i class="fas fa-ruler-vertical"></i>
                                </div>
                                <div class="physical-stat-info-premium">
                                    <div class="physical-stat-label-premium">Height</div>
                                    <div class="physical-stat-value-premium">
                                        <?php if ($user['height']): ?>
                                            <span class="value-premium"><?php echo $user['height']; ?></span>
                                            <span class="unit-premium">cm</span>
                                        <?php else: ?>
                                            <span class="not-available-premium">Not set</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <div class="physical-stat-item-premium">
                                <div class="physical-stat-icon-premium">
                                    <i class="fas fa-weight"></i>
                                </div>
                                <div class="physical-stat-info-premium">
                                    <div class="physical-stat-label-premium">Weight</div>
                                    <div class="physical-stat-value-premium">
                                        <?php if ($user['weight']): ?>
                                            <span class="value-premium"><?php echo $user['weight']; ?></span>
                                            <span class="unit-premium">kg</span>
                                        <?php else: ?>
                                            <span class="not-available-premium">Not set</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Age & Ideal Weight Row -->
                            <div class="physical-stat-item-premium">
                                <div class="physical-stat-icon-premium">
                                    <i class="fas fa-birthday-cake"></i>
                                </div>
                                <div class="physical-stat-info-premium">
                                    <div class="physical-stat-label-premium">Age</div>
                                    <div class="physical-stat-value-premium">
                                        <?php if (isset($user['age']) && $user['age']): ?>
                                            <span class="value-premium"><?php echo $user['age']; ?></span>
                                            <span class="unit-premium">yrs</span>
                                        <?php else: ?>
                                            <span class="not-available-premium">Not set</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <div class="physical-stat-item-premium">
                                <div class="physical-stat-icon-premium">
                                    <i class="fas fa-balance-scale"></i>
                                </div>
                                <div class="physical-stat-info-premium">
                                    <div class="physical-stat-label-premium">Ideal Weight</div>
                                    <div class="physical-stat-value-premium">
                                        <?php if ($user['height']):
                                            // Calculate ideal weight range based on BMI 18.5-24.9
                                            $heightInMeters = $user['height'] / 100;
                                            $minIdealWeight = round(18.5 * $heightInMeters * $heightInMeters);
                                            $maxIdealWeight = round(24.9 * $heightInMeters * $heightInMeters);
                                        ?>
                                            <span class="value-premium"><?php echo $minIdealWeight . '-' . $maxIdealWeight; ?></span>
                                            <span class="unit-premium">kg</span>
                                        <?php else: ?>
                                            <span class="not-available-premium">Not available</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <?php if (!$user['height'] || !$user['weight']): ?>
                            <div class="alert-premium info mt-4 mb-0">
                                <i class="fas fa-info-circle me-3 fa-lg"></i>
                                <div>Some physical stats are missing. Encourage the user to update their profile.</div>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="card-footer-premium text-center">
                        <a href="user_edit.php?id=<?php echo $userId; ?>" class="btn btn-primary btn-premium-sm">
                            <i class="fas fa-edit me-1"></i> Update Physical Stats
                        </a>
                    </div>
                </div>
            </div>

            <!-- Quick Actions & User Summary Card - Fill Blank Space -->
            <div class="col-lg-6 col-md-12 mb-3">
                <div class="card border-0 shadow-sm h-100 premium-card quick-actions-card">
                    <div class="card-header-premium d-flex align-items-center">
                        <div class="icon-circle-premium bg-warning text-white me-3">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <h6 class="m-0 font-weight-bold text-primary">Quick Actions & Summary</h6>
                    </div>

                    <div class="card-body-premium">
                        <!-- User Activity Summary -->
                        <div class="activity-summary-section mb-4">
                            <h6 class="section-title-premium">Activity Overview</h6>
                            <div class="row g-2">
                                <div class="col-6">
                                    <div class="activity-stat-card">
                                        <div class="activity-stat-icon">
                                            <i class="fas fa-graduation-cap text-success"></i>
                                        </div>
                                        <div class="activity-stat-info">
                                            <div class="activity-stat-value"><?php echo $enrollmentsResult ? $enrollmentsResult->num_rows : 0; ?></div>
                                            <div class="activity-stat-label">Courses</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="activity-stat-card">
                                        <div class="activity-stat-icon">
                                            <i class="fas fa-dumbbell text-danger"></i>
                                        </div>
                                        <div class="activity-stat-info">
                                            <div class="activity-stat-value"><?php echo ($workoutsResult ? $workoutsResult->num_rows : 0) + count($recentWorkouts); ?></div>
                                            <div class="activity-stat-label">Workouts</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="activity-stat-card">
                                        <div class="activity-stat-icon">
                                            <i class="fas fa-calendar-check text-info"></i>
                                        </div>
                                        <div class="activity-stat-info">
                                            <div class="activity-stat-value"><?php echo !empty($user['last_login']) ? floor((time() - strtotime($user['last_login'])) / 86400) : 'N/A'; ?></div>
                                            <div class="activity-stat-label">Days Since Login</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="activity-stat-card">
                                        <div class="activity-stat-icon">
                                            <i class="fas fa-mobile-alt text-primary"></i>
                                        </div>
                                        <div class="activity-stat-info">
                                            <div class="activity-stat-value"><?php echo !empty($user['device_id']) ? 'Yes' : 'No'; ?></div>
                                            <div class="activity-stat-label">Device Linked</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="quick-actions-section">
                            <h6 class="section-title-premium">Quick Actions</h6>
                            <div class="quick-actions-grid">
                                <a href="user_edit.php?id=<?php echo $userId; ?>" class="quick-action-btn">
                                    <i class="fas fa-edit"></i>
                                    <span>Edit Profile</span>
                                </a>
                                <a href="video_analytics.php?user_id=<?php echo $userId; ?>" class="quick-action-btn">
                                    <i class="fas fa-chart-line"></i>
                                    <span>View Analytics</span>
                                </a>
                                <?php if (!empty($user['device_id']) && $user['device_is_active']): ?>
                                <button type="button" class="quick-action-btn" onclick="showRevokeDeviceModal()">
                                    <i class="fas fa-ban"></i>
                                    <span>Revoke Device</span>
                                </button>
                                <?php endif; ?>
                                <a href="users.php" class="quick-action-btn">
                                    <i class="fas fa-arrow-left"></i>
                                    <span>Back to Users</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>

        <!-- Second Row: Device Management and Course Enrollments -->
        <div class="row">
            <!-- Comprehensive Device Management Card - Premium Design -->
            <div class="col-lg-6 col-md-12 mb-4">
                <div class="card border-0 shadow-sm h-100 premium-card device-management-card">
                    <div class="card-header-premium d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <div class="icon-circle-premium bg-info text-white me-3">
                                <i class="fas fa-mobile-alt"></i>
                            </div>
                            <h6 class="m-0 font-weight-bold text-primary">Device Session Management</h6>
                        </div>
                        <?php if (!empty($user['device_id']) && $user['device_is_active']): ?>
                        <span class="badge bg-success">
                            <i class="fas fa-circle me-1" style="font-size: 8px;"></i>Active
                        </span>
                        <?php elseif (!empty($user['device_id']) && !$user['device_is_active']): ?>
                        <span class="badge bg-danger">
                            <i class="fas fa-ban me-1"></i>Revoked
                        </span>
                        <?php else: ?>
                        <span class="badge bg-warning">
                            <i class="fas fa-exclamation-triangle me-1"></i>No Device
                        </span>
                        <?php endif; ?>
                    </div>
                    <div class="card-body-premium">
                        <?php if (!empty($user['device_id'])): ?>
                            <!-- Device Information Section -->
                            <div class="device-info-section mb-4">
                                <div class="row g-3">
                                    <div class="col-12">
                                        <div class="device-id-display">
                                            <label class="premium-label">Device ID</label>
                                            <div class="device-id-container-premium">
                                                <div class="device-id-text-premium">
                                                    <?php echo htmlspecialchars($user['device_id']); ?>
                                                </div>
                                                <button type="button" class="btn-copy-premium" onclick="copyToClipboard('<?php echo htmlspecialchars($user['device_id']); ?>')" title="Copy Device ID">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-6">
                                        <div class="device-stat">
                                            <label class="premium-label">First Login</label>
                                            <div class="device-stat-value">
                                                <?php if ($user['device_first_login']): ?>
                                                    <?php echo date('M d, Y', strtotime($user['device_first_login'])); ?>
                                                    <small class="text-muted d-block"><?php echo date('h:i A', strtotime($user['device_first_login'])); ?></small>
                                                <?php else: ?>
                                                    <span class="text-muted">Unknown</span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-6">
                                        <div class="device-stat">
                                            <label class="premium-label">Last Activity</label>
                                            <div class="device-stat-value">
                                                <?php if ($user['device_last_activity']): ?>
                                                    <?php
                                                    $lastActivity = strtotime($user['device_last_activity']);
                                                    $timeDiff = time() - $lastActivity;
                                                    ?>
                                                    <?php echo date('M d, Y', $lastActivity); ?>
                                                    <small class="text-muted d-block">
                                                        <?php
                                                        if ($timeDiff < 3600) {
                                                            echo floor($timeDiff / 60) . ' min ago';
                                                        } elseif ($timeDiff < 86400) {
                                                            echo floor($timeDiff / 3600) . ' hrs ago';
                                                        } else {
                                                            echo floor($timeDiff / 86400) . ' days ago';
                                                        }
                                                        ?>
                                                    </small>
                                                <?php else: ?>
                                                    <span class="text-muted">Unknown</span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-6">
                                        <div class="device-stat">
                                            <label class="premium-label">Active Tokens</label>
                                            <div class="device-stat-value">
                                                <span class="badge bg-primary fs-6"><?php echo $user['active_tokens'] ?? 0; ?></span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-6">
                                        <div class="device-stat">
                                            <label class="premium-label">Session Status</label>
                                            <div class="device-stat-value">
                                                <?php if ($user['device_is_active']): ?>
                                                    <span class="badge bg-success">
                                                        <i class="fas fa-check-circle me-1"></i>Active
                                                    </span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">
                                                        <i class="fas fa-ban me-1"></i>Revoked
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Revocation Information (if device was revoked) -->
                            <?php if (!$user['device_is_active'] && $user['revoked_by_admin']): ?>
                            <div class="revocation-info-section mb-4">
                                <div class="alert alert-warning">
                                    <div class="d-flex align-items-start">
                                        <i class="fas fa-exclamation-triangle me-2 mt-1"></i>
                                        <div>
                                            <strong>Device Access Revoked</strong>
                                            <div class="mt-2">
                                                <small class="text-muted">
                                                    <strong>Revoked by:</strong> <?php echo htmlspecialchars($user['revoked_by_username'] ?? 'Unknown Admin'); ?><br>
                                                    <strong>Date:</strong> <?php echo $user['revoked_at'] ? date('M d, Y h:i A', strtotime($user['revoked_at'])) : 'Unknown'; ?><br>
                                                    <?php if ($user['revocation_reason']): ?>
                                                    <strong>Reason:</strong> <?php echo htmlspecialchars($user['revocation_reason']); ?>
                                                    <?php endif; ?>
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>

                            <!-- Admin Actions -->
                            <div class="device-actions-section">
                                <div class="d-flex gap-2 flex-wrap">
                                    <?php if ($user['device_is_active']): ?>
                                    <button type="button" class="btn btn-danger btn-premium-sm" onclick="showRevokeDeviceModal()">
                                        <i class="fas fa-ban me-1"></i> Revoke Device Access
                                    </button>
                                    <?php endif; ?>

                                    <form method="post" action="user_view.php?id=<?php echo $userId; ?>" class="d-inline" onsubmit="return confirm('Resetting device will require the user to re-register their device. Continue?');">
                                        <input type="hidden" name="reset_device" value="1">
                                        <button type="submit" class="btn btn-warning btn-premium-sm">
                                            <i class="fas fa-redo me-1"></i> Reset Device Registration
                                        </button>
                                    </form>

                                    <?php if (count($deviceRevocationHistory) > 0): ?>
                                    <button type="button" class="btn btn-info btn-premium-sm" onclick="showRevocationHistoryModal()">
                                        <i class="fas fa-history me-1"></i> View History
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php else: ?>
                            <!-- No Device State -->
                            <div class="empty-state-premium">
                                <div class="empty-state-icon-premium">
                                    <i class="fas fa-mobile-alt"></i>
                                </div>
                                <h6 class="mt-3">No Device Registered</h6>
                                <p class="text-muted mb-3">This user has not registered any device yet. They need to log in to the mobile app to register a device.</p>
                                <div class="device-setup-info">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Device registration happens automatically when the user logs in to the mobile app for the first time.
                                    </small>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Course Enrollments - Premium Design (Optimized) -->
            <div class="col-lg-6 col-md-12 mb-3">
        <div class="card border-0 shadow-sm h-100 premium-card course-enrollments-optimized">
            <div class="card-header-premium d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    <div class="icon-circle-premium bg-success text-white me-3">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <h6 class="m-0 font-weight-bold text-primary">Course Enrollments</h6>
                </div>
                <?php if ($enrollmentsResult && $enrollmentsResult->num_rows > 0): ?>
                <span class="badge-count-premium success"><?php echo $enrollmentsResult->num_rows; ?></span>
                <?php endif; ?>
            </div>
            <div class="card-body-premium p-0">
                <?php if ($enrollmentsResult && $enrollmentsResult->num_rows > 0): ?>
                    <div class="course-list-premium">
                        <?php while ($enrollment = $enrollmentsResult->fetch_assoc()): ?>
                            <?php
                            $progress = $enrollment['total_videos'] > 0
                                ? round(($enrollment['completed_videos'] / $enrollment['total_videos']) * 100)
                                : 0;

                            $progressClass = 'danger';
                            if ($progress >= 75) {
                                $progressClass = 'success';
                            } elseif ($progress >= 50) {
                                $progressClass = 'info';
                            } elseif ($progress >= 25) {
                                $progressClass = 'warning';
                            }
                            ?>
                            <div class="course-item-premium">
                                <div class="course-header-premium">
                                    <div class="d-flex align-items-center">
                                        <div class="course-avatar-premium" style="background-color: <?php echo Utilities::getColorFromName($enrollment['title']); ?>;">
                                            <?php echo strtoupper(substr($enrollment['title'], 0, 1)); ?>
                                        </div>
                                        <div class="course-info-premium">
                                            <h6 class="course-title-premium"><?php echo htmlspecialchars($enrollment['title']); ?></h6>
                                            <div class="course-meta-premium">
                                                <span class="course-category-premium"><?php echo htmlspecialchars($enrollment['category']); ?></span>
                                                <span class="course-status-premium <?php echo $enrollment['status']; ?>">
                                                    <?php echo ucfirst($enrollment['status']); ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="course-progress-premium">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span class="progress-label-premium">Progress</span>
                                        <span class="progress-value-premium"><?php echo $progress; ?>%</span>
                                    </div>
                                    <div class="progress-premium">
                                        <div class="progress-bar-premium <?php echo $progressClass; ?>" style="width: <?php echo $progress; ?>%;"></div>
                                    </div>
                                    <div class="course-details-premium">
                                        <div class="course-detail-premium">
                                            <i class="fas fa-calendar-alt"></i> Enrolled: <?php echo date('M d, Y', strtotime($enrollment['start_date'])); ?>
                                        </div>
                                        <div class="course-detail-premium">
                                            <i class="fas fa-video"></i> <?php echo $enrollment['completed_videos']; ?>/<?php echo $enrollment['total_videos']; ?> videos
                                        </div>
                                    </div>
                                </div>

                                <div class="course-actions-premium">
                                    <a href="video_analytics.php?user_id=<?php echo $userId; ?>&course_id=<?php echo $enrollment['id']; ?>" class="btn btn-primary btn-premium-sm">
                                        <i class="fas fa-chart-line me-1"></i> View Analytics
                                    </a>
                                </div>
                            </div>
                        <?php endwhile; ?>
                    </div>
                <?php else: ?>
                    <div class="empty-state-premium">
                        <div class="empty-state-icon-premium">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                        <h6 class="mt-3">No Courses</h6>
                        <p class="text-muted mb-3">This user is not enrolled in any courses yet.</p>
                        <a href="user_edit.php?id=<?php echo $userId; ?>" class="btn btn-primary btn-premium-sm">
                            <i class="fas fa-plus me-1"></i> Assign Course
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

        </div>
    </div>

    <!-- Workout History Row - Optimized -->
    <div class="row">
        <!-- Workout History - Premium Design -->
        <div class="col-12">
        <div class="card border-0 shadow-sm premium-card workout-history-optimized">
            <div class="card-header-premium d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    <div class="icon-circle-premium bg-danger text-white me-3">
                        <i class="fas fa-dumbbell"></i>
                    </div>
                    <h6 class="m-0 font-weight-bold text-primary">Recent Workouts</h6>
                </div>
                <?php if (($workoutsResult && $workoutsResult->num_rows > 0) || !empty($recentWorkouts)): ?>
                <span class="badge-count-premium danger"><?php echo ($workoutsResult ? $workoutsResult->num_rows : 0) + count($recentWorkouts); ?></span>
                <?php endif; ?>
            </div>
            <div class="card-body-premium p-0">
                <?php if (($workoutsResult && $workoutsResult->num_rows > 0) || !empty($recentWorkouts)): ?>
                    <div class="workout-list-premium">
                        <?php if ($workoutsResult && $workoutsResult->num_rows > 0): ?>
                            <?php while ($workout = $workoutsResult->fetch_assoc()): ?>
                                <div class="workout-item-premium">
                                    <div class="workout-icon-premium">
                                        <div class="workout-avatar-premium" style="background-color: <?php echo Utilities::getColorFromName($workout['title']); ?>;">
                                            <i class="fas fa-dumbbell"></i>
                                        </div>
                                    </div>
                                    <div class="workout-content-premium">
                                        <div class="workout-title-premium"><?php echo htmlspecialchars($workout['title']); ?></div>
                                        <div class="workout-meta-premium">
                                            <span class="workout-detail-premium">
                                                <i class="fas fa-clock"></i> <?php echo $workout['duration_minutes']; ?> minutes
                                            </span>
                                            <span class="workout-detail-premium">
                                                <i class="fas fa-calendar-day"></i> <?php echo date('M d, Y', strtotime($workout['date'])); ?>
                                            </span>
                                        </div>
                                    </div>
                                    <?php if (!empty($workout['calories_burned'])): ?>
                                    <div class="workout-stats-premium">
                                        <div class="workout-stat-value-premium"><?php echo $workout['calories_burned']; ?></div>
                                        <div class="workout-stat-label-premium">calories</div>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            <?php endwhile; ?>
                        <?php endif; ?>

                        <?php if (!empty($recentWorkouts)): ?>
                            <?php foreach ($recentWorkouts as $workout): ?>
                                <div class="workout-item-premium">
                                    <div class="workout-icon-premium">
                                        <div class="workout-avatar-premium" style="background-color: #4e73df;">
                                            <i class="fas fa-video"></i>
                                        </div>
                                    </div>
                                    <div class="workout-content-premium">
                                        <div class="workout-title-premium"><?php echo htmlspecialchars($workout['video_title']); ?></div>
                                        <div class="workout-meta-premium">
                                            <?php
                                            $action = $workout['action'];
                                            $duration = $workout['watch_duration'];
                                            $minutes = floor($duration / 60);
                                            $seconds = $duration % 60;
                                            
                                            switch ($action) {
                                                case 'start':
                                                    echo '<span class="workout-detail-premium"><i class="fas fa-play"></i> Started watching</span>';
                                                    break;
                                                case 'progress':
                                                    echo '<span class="workout-detail-premium"><i class="fas fa-clock"></i> ' . $minutes . 'm ' . $seconds . 's watched</span>';
                                                    break;
                                                case 'complete':
                                                    echo '<span class="workout-detail-premium"><i class="fas fa-check"></i> Completed workout</span>';
                                                    break;
                                                default:
                                                    echo '<span class="workout-detail-premium"><i class="fas fa-info-circle"></i> Activity recorded</span>';
                                            }
                                            ?>
                                            <span class="workout-detail-premium">
                                                <i class="fas fa-calendar-day"></i> <?php echo date('M d, Y', strtotime($workout['created_at'])); ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <div class="empty-state-premium">
                        <div class="empty-state-icon-premium">
                            <i class="fas fa-dumbbell"></i>
                        </div>
                        <h6 class="mt-3">No Workouts</h6>
                        <p class="text-muted mb-3">This user hasn't completed any workouts yet.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Global Admin Footer -->
<footer class="admin-footer">
    <div class="container-fluid">
        <p>&copy; <?php echo date('Y'); ?> KFT Admin Dashboard. All rights reserved.</p>
    </div>
</footer>

<!-- Device Revocation Modal -->
<div class="modal fade" id="revokeDeviceModal" tabindex="-1" aria-labelledby="revokeDeviceModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content premium-modal">
            <div class="modal-header-premium">
                <h5 class="modal-title-premium" id="revokeDeviceModalLabel">
                    <i class="fas fa-ban me-2 text-danger"></i> Revoke Device Access
                </h5>
                <button type="button" class="btn-close-premium" data-bs-dismiss="modal" aria-label="Close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body-premium">
                <div class="alert alert-warning">
                    <div class="d-flex align-items-start">
                        <i class="fas fa-exclamation-triangle me-2 mt-1"></i>
                        <div>
                            <strong>Warning: This action will immediately log out the user</strong>
                            <p class="mb-0 mt-2">
                                This will invalidate all authentication tokens for this device and force the user to log in again.
                                The user will see a message that their session was terminated by an administrator.
                            </p>
                        </div>
                    </div>
                </div>

                <div class="device-revocation-info mb-3">
                    <div class="row">
                        <div class="col-sm-4"><strong>User:</strong></div>
                        <div class="col-sm-8"><?php echo htmlspecialchars($user['name']); ?></div>
                    </div>
                    <div class="row">
                        <div class="col-sm-4"><strong>Device ID:</strong></div>
                        <div class="col-sm-8">
                            <code class="device-id-code"><?php echo htmlspecialchars($user['device_id'] ?? 'N/A'); ?></code>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-4"><strong>Active Tokens:</strong></div>
                        <div class="col-sm-8">
                            <span class="badge bg-primary"><?php echo $user['active_tokens'] ?? 0; ?></span>
                        </div>
                    </div>
                </div>

                <form id="revokeDeviceForm">
                    <div class="mb-3">
                        <label for="revocationReason" class="form-label">Reason for revocation <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="revocationReason" name="reason" rows="3"
                                  placeholder="Enter the reason for revoking device access..." required>Device access revoked by administrator</textarea>
                        <div class="form-text">This reason will be logged for audit purposes and may be visible to the user.</div>
                    </div>
                </form>

                <div id="revocationMessage" class="alert" style="display: none;"></div>
            </div>
            <div class="modal-footer-premium">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i> Cancel
                </button>
                <button type="button" class="btn btn-danger" id="confirmRevokeBtn" onclick="confirmDeviceRevocation()">
                    <i class="fas fa-ban me-1"></i> Revoke Device Access
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Device Revocation History Modal -->
<div class="modal fade" id="revocationHistoryModal" tabindex="-1" aria-labelledby="revocationHistoryModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content premium-modal">
            <div class="modal-header-premium">
                <h5 class="modal-title-premium" id="revocationHistoryModalLabel">
                    <i class="fas fa-history me-2"></i> Device Revocation History
                </h5>
                <button type="button" class="btn-close-premium" data-bs-dismiss="modal" aria-label="Close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body-premium">
                <?php if (count($deviceRevocationHistory) > 0): ?>
                <div class="revocation-history-list">
                    <?php foreach ($deviceRevocationHistory as $history): ?>
                    <div class="revocation-history-item">
                        <div class="d-flex align-items-start">
                            <div class="revocation-icon">
                                <i class="fas fa-ban text-danger"></i>
                            </div>
                            <div class="revocation-content flex-grow-1">
                                <div class="revocation-header">
                                    <strong>Device Access Revoked</strong>
                                    <span class="revocation-date"><?php echo date('M d, Y h:i A', strtotime($history['created_at'])); ?></span>
                                </div>
                                <div class="revocation-details">
                                    <div class="row g-2">
                                        <div class="col-sm-6">
                                            <small class="text-muted">
                                                <strong>Admin:</strong> <?php echo htmlspecialchars($history['admin_name'] ?? $history['admin_username']); ?>
                                            </small>
                                        </div>
                                        <div class="col-sm-6">
                                            <small class="text-muted">
                                                <strong>Device:</strong> <code><?php echo htmlspecialchars(substr($history['target_device_id'], 0, 16)); ?>...</code>
                                            </small>
                                        </div>
                                    </div>
                                    <?php if ($history['reason']): ?>
                                    <div class="revocation-reason mt-2">
                                        <small class="text-muted">
                                            <strong>Reason:</strong> <?php echo htmlspecialchars($history['reason']); ?>
                                        </small>
                                    </div>
                                    <?php endif; ?>
                                    <?php if ($history['action_details']): ?>
                                    <?php $details = json_decode($history['action_details'], true); ?>
                                    <div class="revocation-stats mt-2">
                                        <small class="text-muted">
                                            <i class="fas fa-key me-1"></i> <?php echo $details['revoked_tokens'] ?? 0; ?> tokens revoked
                                            <i class="fas fa-mobile-alt ms-2 me-1"></i> <?php echo $details['revoked_sessions'] ?? 0; ?> sessions revoked
                                        </small>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php else: ?>
                <div class="empty-state-premium">
                    <div class="empty-state-icon-premium">
                        <i class="fas fa-history"></i>
                    </div>
                    <h6 class="mt-3">No Revocation History</h6>
                    <p class="text-muted mb-0">This user's device has never been revoked by an administrator.</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
// Function to copy text to clipboard
function copyToClipboard(text) {
    const textarea = document.createElement('textarea');
    textarea.value = text;
    document.body.appendChild(textarea);
    textarea.select();
    document.execCommand('copy');
    document.body.removeChild(textarea);

    // Show a tooltip or some indication that the text was copied
    alert('Device ID copied to clipboard!');
}

// Enhanced PIN Management Functions
function copyPinToClipboard(pin, button) {
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(pin).then(() => {
            showPinFeedback(button, 'Copied!', 'fas fa-check', 'success');
        }).catch(() => {
            fallbackCopyToClipboard(pin, button);
        });
    } else {
        fallbackCopyToClipboard(pin, button);
    }
}

function fallbackCopyToClipboard(pin, button) {
    const textarea = document.createElement('textarea');
    textarea.value = pin;
    textarea.style.position = 'fixed';
    textarea.style.opacity = '0';
    document.body.appendChild(textarea);
    textarea.select();
    document.execCommand('copy');
    document.body.removeChild(textarea);
    showPinFeedback(button, 'Copied!', 'fas fa-check', 'success');
}

function togglePinVisibility(button) {
    const dotsDisplay = document.getElementById('pin-dots-display');
    const actualValue = document.getElementById('pin-actual-value');
    const icon = button.querySelector('i');
    const text = button.querySelector('.btn-text-premium');

    if (dotsDisplay.style.display === 'none') {
        // Hide PIN
        dotsDisplay.style.display = 'flex';
        actualValue.style.display = 'none';
        icon.className = 'fas fa-eye';
        text.textContent = 'Show';
        button.setAttribute('title', 'Show PIN');
        showPinFeedback(button, 'Hidden', 'fas fa-eye-slash', 'info', 1000);
    } else {
        // Show PIN
        dotsDisplay.style.display = 'none';
        actualValue.style.display = 'flex';
        icon.className = 'fas fa-eye-slash';
        text.textContent = 'Hide';
        button.setAttribute('title', 'Hide PIN');
        showPinFeedback(button, 'Visible', 'fas fa-eye', 'warning', 1000);

        // Auto-hide after 10 seconds for security
        setTimeout(() => {
            if (actualValue.style.display !== 'none') {
                dotsDisplay.style.display = 'flex';
                actualValue.style.display = 'none';
                icon.className = 'fas fa-eye';
                text.textContent = 'Show';
                button.setAttribute('title', 'Show PIN');
                showPinFeedback(button, 'Auto-hidden', 'fas fa-shield-alt', 'secondary', 1500);
            }
        }, 10000);
    }
}

function showPinFeedback(button, message, iconClass, type, duration = 1200) {
    const originalIcon = button.querySelector('i').className;
    const originalText = button.querySelector('.btn-text-premium').textContent;
    const originalClass = button.className;

    // Update button appearance
    button.querySelector('i').className = iconClass;
    button.querySelector('.btn-text-premium').textContent = message;
    button.classList.add(`pin-action-btn-${type}`);

    // Reset after duration
    setTimeout(() => {
        button.querySelector('i').className = originalIcon;
        button.querySelector('.btn-text-premium').textContent = originalText;
        button.className = originalClass;
    }, duration);
}

// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl, {
            delay: { show: 500, hide: 100 }
        });
    });

    // Make toggle container clickable
    const toggleContainers = document.querySelectorAll('.toggle-container-premium');
    toggleContainers.forEach(container => {
        container.addEventListener('click', function(e) {
            // Prevent default only if clicking on the container but not on the button itself
            if (e.target !== this.querySelector('button') && !this.querySelector('button').contains(e.target)) {
                e.preventDefault();
                // Trigger the button click
                this.querySelector('button').click();
            }
        });
    });
});



// Device Management Functions
function showRevokeDeviceModal() {
    const modal = new bootstrap.Modal(document.getElementById('revokeDeviceModal'));
    modal.show();
}

function showRevocationHistoryModal() {
    const modal = new bootstrap.Modal(document.getElementById('revocationHistoryModal'));
    modal.show();
}

function confirmDeviceRevocation() {
    const reason = document.getElementById('revocationReason').value.trim();
    const messageDiv = document.getElementById('revocationMessage');
    const confirmBtn = document.getElementById('confirmRevokeBtn');

    if (!reason) {
        showRevocationMessage('Please provide a reason for revocation.', 'danger');
        return;
    }

    // Disable button and show loading
    const originalBtnText = confirmBtn.innerHTML;
    confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Revoking...';
    confirmBtn.disabled = true;

    // Hide any previous messages
    messageDiv.style.display = 'none';

    // Prepare form data
    const formData = new FormData();
    formData.append('action', 'revoke_device');
    formData.append('device_id', '<?php echo htmlspecialchars($user['device_id'] ?? ''); ?>');
    formData.append('reason', reason);

    // Send AJAX request
    fetch('user_view.php?id=<?php echo $userId; ?>', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showRevocationMessage('Device access revoked successfully! The user will be logged out on their next app interaction.', 'success');

            // Auto-close modal and refresh page after 2 seconds
            setTimeout(() => {
                location.reload();
            }, 2000);
        } else {
            showRevocationMessage('Failed to revoke device: ' + (data.error || 'Unknown error'), 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showRevocationMessage('Network error occurred. Please try again.', 'danger');
    })
    .finally(() => {
        // Re-enable button
        confirmBtn.innerHTML = originalBtnText;
        confirmBtn.disabled = false;
    });
}

function showRevocationMessage(message, type) {
    const messageDiv = document.getElementById('revocationMessage');
    messageDiv.className = `alert alert-${type}`;
    messageDiv.innerHTML = `<i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>${message}`;
    messageDiv.style.display = 'block';
}

document.getElementById('changePinForm').addEventListener('submit', function(e) {
    e.preventDefault();
    var newPin = document.getElementById('newPin').value.trim().replace(/[^\d]/g, '');
    var confirmPin = document.getElementById('confirmPin').value.trim().replace(/[^\d]/g, '');
    var msg = document.getElementById('pinChangeMsg');
    msg.textContent = '';
    if (newPin.length !== 4) {
        msg.textContent = 'PIN must be exactly 4 digits (numbers only, no spaces).';
        document.getElementById('newPin').focus();
        return;
    }
    if (newPin !== confirmPin) {
        msg.textContent = 'PINs do not match.';
        document.getElementById('confirmPin').focus();
        return;
    }
    // AJAX request to update PIN
    var xhr = new XMLHttpRequest();
    xhr.open('POST', 'user_update_pin.php', true);
    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            if (xhr.status === 200) {
                try {
                    var res = JSON.parse(xhr.responseText);
                    if (res.success) {
                        msg.classList.remove('text-danger');
                        msg.classList.add('text-success');
                        msg.textContent = 'PIN updated successfully!';

                        // Update the new PIN display
                        const pinActualValue = document.getElementById('pin-actual-value');
                        if (pinActualValue) {
                            pinActualValue.textContent = res.pin;
                        }

                        // Update the modal field
                        const currentPinField = document.getElementById('currentPinField');
                        if (currentPinField) {
                            currentPinField.value = res.pin;
                        }
                        document.getElementById('pinExpiryText').innerHTML = 'Expires at: ' + new Date(res.expires_at.replace(' ', 'T')).toLocaleString();
                        setTimeout(function() {
                            var modal = bootstrap.Modal.getInstance(document.getElementById('changePinModal'));
                            modal.hide();
                            msg.textContent = '';
                        }, 1200);
                    } else {
                        msg.classList.remove('text-success');
                        msg.classList.add('text-danger');
                        msg.textContent = res.message || 'Failed to update PIN.';
                    }
                } catch (e) {
                    msg.classList.remove('text-success');
                    msg.classList.add('text-danger');
                    msg.textContent = 'Failed to update PIN.';
                }
            } else {
                msg.classList.remove('text-success');
                msg.classList.add('text-danger');
                msg.textContent = 'Server error.';
            }
        }
    };
    xhr.send('user_id=<?php echo $userId; ?>&new_pin=' + encodeURIComponent(newPin));
});
</script>

<style>
:root {
    --primary: #4e73df;
    --success: #1cc88a;
    --info: #36b9cc;
    --warning: #f6c23e;
    --danger: #e74a3b;
    --secondary: #858796;
    --light: #f8f9fa;
    --dark: #5a5c69;

    /* New Professional Color Scheme */
    --bg-primary: #2c3e50;
    --bg-secondary: #34495e;
    --bg-light: #ecf0f1;
    --bg-dark: #1a252f;
    --text-primary: #2c3e50;
    --text-secondary: #7f8c8d;
    --border-color: #bdc3c7;
    --card-bg: #ffffff;
    --header-bg: #34495e;
}

/* ===== PROFESSIONAL BACKEND STYLING ===== */

/* Change backend background to lite dark grey */
body {
    background-color: #ecf0f1 !important;
    color: var(--text-primary) !important;
}

#page-content-wrapper {
    background-color: #ecf0f1 !important;
    min-height: 100vh;
}

/* Remove excessive padding from main container */
#page-content-wrapper .container-fluid {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
    padding-left: 1rem !important;
    padding-right: 1rem !important;
    background-color: transparent !important;
}

/* ===== REDESIGNED TOP HEADER - MINIMALISTIC & PROFESSIONAL ===== */
.user-profile-header {
    margin-bottom: 1.25rem !important;
}

.profile-header-premium {
    background: linear-gradient(135deg, var(--header-bg) 0%, #2c3e50 100%) !important;
    padding: 1.25rem 1.5rem !important;
    margin-bottom: 0;
    border-radius: 12px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.profile-header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
}

.profile-avatar-premium {
    position: relative;
    flex-shrink: 0;
}

.profile-img-premium,
.profile-initials-premium {
    width: 80px !important;
    height: 80px !important;
    border-radius: 50% !important;
    border: 4px solid rgba(255, 255, 255, 0.2) !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;
}

.profile-initials-premium {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 2rem !important;
    font-weight: 700 !important;
    color: white !important;
}

.profile-info-premium {
    flex: 1;
    min-width: 300px;
}

.profile-name-premium {
    color: white !important;
    font-size: 1.75rem !important;
    font-weight: 700 !important;
    margin-bottom: 0.5rem !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
}

.profile-meta-premium {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin-bottom: 1rem;
    color: rgba(255, 255, 255, 0.9) !important;
    font-size: 0.9rem;
}

.profile-username-premium {
    font-weight: 600;
    color: rgba(255, 255, 255, 0.95) !important;
}

.profile-divider {
    color: rgba(255, 255, 255, 0.6) !important;
    font-weight: bold;
}

.profile-joined,
.profile-last-login {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: rgba(255, 255, 255, 0.85) !important;
}

.profile-badges-premium {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.badge-premium {
    background: rgba(255, 255, 255, 0.15) !important;
    color: white !important;
    padding: 0.4rem 0.8rem !important;
    border-radius: 20px !important;
    font-size: 0.8rem !important;
    font-weight: 500 !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    backdrop-filter: blur(10px) !important;
}

.badge-premium.info {
    background: rgba(54, 185, 204, 0.2) !important;
    border-color: rgba(54, 185, 204, 0.3) !important;
}

.badge-premium.success {
    background: rgba(28, 200, 138, 0.2) !important;
    border-color: rgba(28, 200, 138, 0.3) !important;
}

.badge-premium.warning {
    background: rgba(246, 194, 62, 0.2) !important;
    border-color: rgba(246, 194, 62, 0.3) !important;
}

.profile-header-actions-premium {
    flex-shrink: 0;
}

.btn-premium {
    padding: 0.6rem 1.2rem !important;
    border-radius: 8px !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    border: none !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

.btn-premium:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.25) !important;
}

/* Eliminate gaps between cards */
.row {
    margin-bottom: 0 !important;
    row-gap: 1.25rem;
}

.row + .row {
    margin-top: 1.25rem;
}

/* Optimize card spacing */
.premium-card {
    transition: all 0.3s ease;
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 0 !important;
}

.premium-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

/* Remove unnecessary margins from columns */
[class*="col-"] {
    margin-bottom: 0 !important;
}

/* Consistent card heights */
.h-100 {
    height: 100% !important;
}

/* ===== STAFF ASSIGNMENT CARD OPTIMIZATION ===== */
.staff-assignment-card-compact {
    max-height: 280px !important;
}

.staff-assignment-card-compact .card-body-premium {
    padding: 1rem !important;
}

.staff-assignment-card-compact .premium-label {
    font-size: 0.85rem !important;
    font-weight: 600 !important;
    color: var(--text-secondary) !important;
    margin-bottom: 0.5rem !important;
}

.staff-assignment-card-compact .avatar-circle-premium {
    width: 32px !important;
    height: 32px !important;
    font-size: 0.9rem !important;
}

.staff-assignment-card-compact .form-select-premium {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.9rem !important;
    border-radius: 6px !important;
    border: 1px solid var(--border-color) !important;
}

.staff-assignment-card-compact .btn-premium-sm {
    padding: 0.5rem 1rem !important;
    font-size: 0.85rem !important;
}

/* Fix status indicator positioning */
.status-indicator-premium {
    position: absolute;
    bottom: 8px;
    right: 8px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 3px solid #fff;
    z-index: 2;
}

.status-indicator-premium.active {
    background-color: #28a745;
}

.status-indicator-premium.inactive {
    background-color: #dc3545;
}

.status-badge-premium {
    position: absolute;
    bottom: 10px;
    right: 10px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 3px solid #fff;
    z-index: 2;
}

.status-badge-premium.active {
    background-color: #28a745;
}

.status-badge-premium.inactive {
    background-color: #dc3545;
}

/* Optimize card layout for better space utilization */
.col-md-4 .premium-card,
.col-md-6 .premium-card,
.col-md-8 .premium-card,
.col-lg-6 .premium-card,
.col-lg-12 .premium-card {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.card-body-premium {
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* Compact Physical Stats area */
.physical-stats-card-premium .card-body-premium {
    padding: 1rem;
}

.bmi-gauge-container-premium {
    margin-bottom: 1rem;
}

/* Optimize Course Enrollments card to fill space */
.course-list-premium {
    max-height: 400px;
    overflow-y: auto;
}

.course-item-premium {
    padding: 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.course-item-premium:last-child {
    border-bottom: none;
}

/* Optimize Workout History card */
.workout-list-premium {
    max-height: 350px;
    overflow-y: auto;
}

.workout-item-premium {
    padding: 0.875rem 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.workout-item-premium:last-child {
    border-bottom: none;
}

/* Mobile responsive improvements */
@media (max-width: 768px) {
    #page-content-wrapper .container-fluid {
        padding-left: 0.75rem !important;
        padding-right: 0.75rem !important;
        padding-top: 0.75rem !important;
    }

    .profile-header-premium {
        padding: 1rem;
    }

    .row {
        row-gap: 1rem;
    }

    .row + .row {
        margin-top: 1rem;
    }

    .card-header-premium {
        padding: 0.875rem 1rem;
    }

    .card-body-premium {
        padding: 0.875rem 1rem;
    }

    .card-footer-premium {
        padding: 0.75rem 1rem;
    }
}

/* Tablet responsive improvements */
@media (min-width: 768px) and (max-width: 991.98px) {
    /* Tablet responsive improvements */
    #sidebar-wrapper {
        width: 200px !important;
    }

    #page-content-wrapper {
        margin-left: 200px !important;
    }

    .col-lg-6 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    .row {
        row-gap: 1.125rem;
    }

    .row + .row {
        margin-top: 1.125rem;
    }

    .profile-header-premium {
        padding: 1.25rem !important;
    }

    .profile-img-premium,
    .profile-initials-premium {
        width: 70px !important;
        height: 70px !important;
    }

    .profile-name-premium {
        font-size: 1.5rem !important;
    }

    .quick-actions-grid {
        grid-template-columns: repeat(2, 1fr) !important;
    }

    .activity-summary-section .row {
        row-gap: 0.5rem !important;
    }
}

/* Desktop improvements */
@media (min-width: 992px) {
    .col-lg-6 {
        flex: 0 0 50%;
        max-width: 50%;
    }

    .col-lg-12 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    .row {
        row-gap: 1.5rem;
    }

    .row + .row {
        margin-top: 1.5rem;
    }
}

/* Global footer styling */
.admin-footer {
    margin-top: 2rem;
    padding: 1.5rem 0;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    background-color: #f8f9fa;
    text-align: center;
    color: #6c757d;
    font-size: 0.875rem;
}

.admin-footer p {
    margin: 0;
}

/* Remove additional spacing elements */
.mb-4:last-child {
    margin-bottom: 0 !important;
}

/* ===== OPTIMIZED CARD SPECIFIC STYLES ===== */

/* Course Enrollments Optimization */
.course-enrollments-optimized .course-list-premium {
    max-height: 450px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

.course-enrollments-optimized .course-list-premium::-webkit-scrollbar {
    width: 6px;
}

.course-enrollments-optimized .course-list-premium::-webkit-scrollbar-track {
    background: transparent;
}

.course-enrollments-optimized .course-list-premium::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

.course-enrollments-optimized .course-item-premium {
    padding: 0.875rem 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: background-color 0.2s ease;
}

.course-enrollments-optimized .course-item-premium:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.course-enrollments-optimized .course-item-premium:last-child {
    border-bottom: none;
}

/* Workout History Optimization */
.workout-history-optimized .workout-list-premium {
    max-height: 400px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

.workout-history-optimized .workout-list-premium::-webkit-scrollbar {
    width: 6px;
}

.workout-history-optimized .workout-list-premium::-webkit-scrollbar-track {
    background: transparent;
}

.workout-history-optimized .workout-list-premium::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

.workout-history-optimized .workout-item-premium {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: background-color 0.2s ease;
}

.workout-history-optimized .workout-item-premium:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.workout-history-optimized .workout-item-premium:last-child {
    border-bottom: none;
}

/* Physical Stats Compact Layout */
.physical-stats-card-premium .bmi-gauge-container-premium {
    margin-bottom: 0.75rem;
}

.physical-stats-card-premium .physical-stats-grid-premium {
    gap: 0.75rem;
}

.physical-stats-card-premium .physical-stat-item-premium {
    padding: 0.5rem;
    background: rgba(0, 0, 0, 0.02);
    border-radius: 8px;
    margin-bottom: 0.5rem;
}

/* Device Management Compact Layout */
.device-info-section .row {
    row-gap: 0.75rem;
}

.device-stat {
    padding: 0.5rem;
    background: rgba(0, 0, 0, 0.02);
    border-radius: 6px;
}

/* ===== QUICK ACTIONS CARD STYLES ===== */
.quick-actions-card .card-body-premium {
    padding: 1rem;
}

.section-title-premium {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid rgba(0, 0, 0, 0.05);
}

.activity-summary-section {
    margin-bottom: 1.5rem;
}

.activity-stat-card {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    background: rgba(0, 0, 0, 0.02);
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
}

.activity-stat-card:hover {
    background: rgba(0, 0, 0, 0.04);
    transform: translateY(-1px);
}

.activity-stat-icon {
    margin-right: 0.75rem;
    font-size: 1.2rem;
}

.activity-stat-info {
    flex: 1;
}

.activity-stat-value {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1.2;
}

.activity-stat-label {
    font-size: 0.75rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
}

.quick-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 1rem 0.75rem;
    background: rgba(78, 115, 223, 0.1);
    border: 1px solid rgba(78, 115, 223, 0.2);
    border-radius: 8px;
    text-decoration: none;
    color: var(--primary);
    transition: all 0.3s ease;
    font-size: 0.85rem;
    font-weight: 500;
    min-height: 70px;
}

.quick-action-btn:hover {
    background: rgba(78, 115, 223, 0.15);
    border-color: rgba(78, 115, 223, 0.3);
    color: var(--primary);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(78, 115, 223, 0.2);
}

.quick-action-btn i {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
}

.quick-action-btn span {
    text-align: center;
    line-height: 1.2;
}

/* ===== RESPONSIVE SIDE PANEL & LAYOUT IMPROVEMENTS ===== */

/* Side panel responsive fixes */
#sidebar-wrapper {
    transition: all 0.3s ease !important;
}

@media (max-width: 768px) {
    #sidebar-wrapper {
        margin-left: -250px !important;
        position: fixed !important;
        z-index: 1050 !important;
        height: 100vh !important;
        overflow-y: auto !important;
    }

    #sidebar-wrapper.active {
        margin-left: 0 !important;
    }

    #page-content-wrapper {
        width: 100% !important;
        margin-left: 0 !important;
    }

    .sidebar-toggle {
        display: block !important;
    }

    /* Mobile layout optimizations */
    .profile-header-premium {
        padding: 1rem !important;
        flex-direction: column !important;
        text-align: center !important;
    }

    .profile-header-content {
        flex-direction: column !important;
        align-items: center !important;
        gap: 1rem !important;
    }

    .profile-info-premium {
        text-align: center !important;
        min-width: auto !important;
    }

    .profile-meta-premium {
        justify-content: center !important;
        flex-wrap: wrap !important;
    }

    .profile-badges-premium {
        justify-content: center !important;
    }

    .course-enrollments-optimized .course-list-premium,
    .workout-history-optimized .workout-list-premium {
        max-height: 300px;
    }

    .course-enrollments-optimized .course-item-premium,
    .workout-history-optimized .workout-item-premium {
        padding: 0.75rem;
    }

    .physical-stats-card-premium .card-body-premium {
        padding: 0.875rem;
    }

    .quick-actions-grid {
        grid-template-columns: 1fr !important;
        gap: 0.5rem !important;
    }

    .quick-action-btn {
        flex-direction: row !important;
        justify-content: flex-start !important;
        padding: 0.75rem !important;
        min-height: auto !important;
    }

    .quick-action-btn i {
        margin-bottom: 0 !important;
        margin-right: 0.5rem !important;
        font-size: 1rem !important;
    }

    .activity-stat-card {
        padding: 0.5rem !important;
    }

    .activity-stat-value {
        font-size: 1rem !important;
    }
}

@media (min-width: 1200px) {
    .course-enrollments-optimized .course-list-premium {
        max-height: 500px;
    }

    .workout-history-optimized .workout-list-premium {
        max-height: 450px;
    }
}

/* Icon Circle */
.icon-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

/* User Profile Card */
.user-profile-card {
    overflow: hidden;
    transition: all 0.3s ease;
}

.user-profile-card:hover {
    transform: translateY(-5px);
}

/* Device Management Styles */
.device-management-card {
    border-left: 4px solid var(--info) !important;
}

.device-info-section {
    background: rgba(54, 185, 204, 0.05);
    border-radius: 8px;
    padding: 1rem;
    border: 1px solid rgba(54, 185, 204, 0.1);
}

.device-stat {
    text-align: center;
}

.device-stat .premium-label {
    font-size: 0.75rem;
    color: var(--secondary);
    margin-bottom: 0.5rem;
    display: block;
}

.device-stat-value {
    font-weight: 600;
    color: var(--dark);
}

.device-id-container-premium {
    display: flex;
    align-items: center;
    background: white;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 6px;
    padding: 0.5rem;
    margin-top: 0.25rem;
}

.device-id-text-premium {
    flex: 1;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    color: var(--dark);
    word-break: break-all;
    padding-right: 0.5rem;
}

.btn-copy-premium {
    background: var(--primary);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 0.25rem 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.75rem;
}

.btn-copy-premium:hover {
    background: var(--dark);
    transform: scale(1.05);
}

.revocation-info-section .alert {
    border-left: 4px solid var(--warning);
}

.device-actions-section {
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    padding-top: 1rem;
}

.device-id-code {
    background: rgba(78, 115, 223, 0.1);
    color: var(--primary);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
}

/* Revocation History Styles */
.revocation-history-list {
    max-height: 400px;
    overflow-y: auto;
}

.revocation-history-item {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 1rem 0;
}

.revocation-history-item:last-child {
    border-bottom: none;
}

.revocation-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(231, 74, 59, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
}

.revocation-content {
    flex: 1;
}

.revocation-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.revocation-date {
    font-size: 0.75rem;
    color: var(--secondary);
    margin-left: auto;
}

.revocation-details {
    font-size: 0.85rem;
}

.revocation-reason {
    background: rgba(246, 194, 62, 0.1);
    border-radius: 4px;
    padding: 0.5rem;
    border-left: 3px solid var(--warning);
}

.revocation-stats {
    background: rgba(54, 185, 204, 0.1);
    border-radius: 4px;
    padding: 0.5rem;
    border-left: 3px solid var(--info);
}

/* Modal Enhancements */
.premium-modal .modal-header-premium {
    background: linear-gradient(135deg, var(--primary), #6c5ce7);
    color: white;
    border-bottom: none;
    border-radius: 0.5rem 0.5rem 0 0;
}

.premium-modal .modal-title-premium {
    font-weight: 600;
}

.premium-modal .btn-close-premium {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    opacity: 0.8;
    transition: opacity 0.2s ease;
}

.premium-modal .btn-close-premium:hover {
    opacity: 1;
}

.premium-modal .modal-body-premium {
    padding: 1.5rem;
}

.premium-modal .modal-footer-premium {
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    padding: 1rem 1.5rem;
}

.device-revocation-info {
    background: rgba(54, 185, 204, 0.05);
    border-radius: 6px;
    padding: 1rem;
    border: 1px solid rgba(54, 185, 204, 0.2);
}

.device-revocation-info .row {
    margin-bottom: 0.5rem;
}

.device-revocation-info .row:last-child {
    margin-bottom: 0;
}

.profile-header {
    position: relative;
    background: linear-gradient(to right, rgba(78, 115, 223, 0.05), rgba(78, 115, 223, 0.1));
}

.user-avatar-large {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 40px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    border: 4px solid white;
}

.status-indicator {
    position: absolute;
    bottom: 10px;
    right: calc(50% - 60px);
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 3px solid white;
}

.status-indicator.active {
    background-color: var(--success);
}

.status-indicator.inactive {
    background-color: var(--secondary);
}

/* Enhanced PIN Management Styles */
.pin-management-section-premium {
    background: linear-gradient(135deg, rgba(78, 115, 223, 0.02), rgba(78, 115, 223, 0.05));
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
    border: 1px solid rgba(78, 115, 223, 0.1);
    transition: all 0.3s ease;
}

.pin-management-section-premium:hover {
    box-shadow: 0 4px 15px rgba(78, 115, 223, 0.1);
    transform: translateY(-2px);
}

.pin-header-premium {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid rgba(78, 115, 223, 0.1);
}

.pin-icon-container-premium {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary), #6c5ce7);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    box-shadow: 0 4px 10px rgba(78, 115, 223, 0.3);
}

.pin-title-section-premium {
    flex: 1;
    margin-left: 15px;
}

.pin-title-premium {
    font-size: 18px;
    font-weight: 600;
    color: var(--dark);
    margin: 0;
    line-height: 1.2;
}

.pin-subtitle-premium {
    font-size: 13px;
    color: var(--secondary);
    margin: 2px 0 0 0;
    line-height: 1.3;
}

.pin-security-badge-premium {
    display: flex;
    align-items: center;
    background: rgba(28, 200, 138, 0.1);
    color: var(--success);
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    border: 1px solid rgba(28, 200, 138, 0.2);
}

.pin-security-badge-premium i {
    margin-right: 5px;
    font-size: 11px;
}

.pin-status-card-premium {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.pin-active-premium {
    border-left: 4px solid var(--success);
}

.pin-inactive-premium {
    border-left: 4px solid var(--danger);
}

.pin-status-header-premium {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.pin-status-indicator-premium {
    display: flex;
    align-items: center;
    color: var(--success);
    font-weight: 500;
}

.pin-status-indicator-premium i {
    margin-right: 8px;
    font-size: 16px;
}

.pin-expiry-info-premium {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: var(--secondary);
}

.pin-expiry-info-premium i {
    margin-right: 5px;
}

.pin-display-section-premium {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding: 15px;
    background: rgba(248, 249, 250, 0.8);
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.pin-value-container-premium {
    flex: 1;
}

.pin-dots-premium {
    display: flex;
    align-items: center;
    gap: 8px;
}

.pin-dot-premium {
    width: 12px;
    height: 12px;
    background: var(--primary);
    border-radius: 50%;
    display: inline-block;
    animation: pinDotPulse 2s infinite;
}

.pin-dot-premium:nth-child(2) {
    animation-delay: 0.2s;
}

.pin-dot-premium:nth-child(3) {
    animation-delay: 0.4s;
}

.pin-dot-premium:nth-child(4) {
    animation-delay: 0.6s;
}

@keyframes pinDotPulse {
    0%, 100% { opacity: 0.6; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.1); }
}

.pin-actual-value-premium {
    font-family: 'Courier New', monospace;
    font-size: 24px;
    font-weight: bold;
    color: var(--primary);
    letter-spacing: 8px;
    padding: 5px 10px;
    background: white;
    border-radius: 6px;
    border: 2px solid var(--primary);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 120px;
    box-shadow: 0 2px 4px rgba(78, 115, 223, 0.1);
}

.pin-actions-premium {
    display: flex;
    gap: 8px;
    margin-left: 15px;
}

.pin-action-btn-premium {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    border: none;
    border-radius: 10px;
    background: white;
    color: var(--primary);
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(78, 115, 223, 0.2);
    position: relative;
    overflow: hidden;
}

.pin-action-btn-premium:hover {
    background: var(--primary);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(78, 115, 223, 0.3);
}

.pin-action-btn-premium:active {
    transform: translateY(0);
}

.pin-action-btn-premium i {
    font-size: 16px;
    margin-bottom: 2px;
}

.btn-text-premium {
    font-size: 10px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.pin-action-btn-success {
    background: var(--success) !important;
    color: white !important;
    border-color: var(--success) !important;
}

.pin-action-btn-warning {
    background: var(--warning) !important;
    color: white !important;
    border-color: var(--warning) !important;
}

.pin-action-btn-info {
    background: var(--info) !important;
    color: white !important;
    border-color: var(--info) !important;
}

.pin-action-btn-secondary {
    background: var(--secondary) !important;
    color: white !important;
    border-color: var(--secondary) !important;
}

.pin-security-notice-premium {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    background: rgba(54, 185, 204, 0.05);
    border: 1px solid rgba(54, 185, 204, 0.2);
    border-radius: 8px;
    color: var(--info);
    font-size: 12px;
    line-height: 1.4;
}

.pin-security-notice-premium i {
    margin-right: 8px;
    font-size: 14px;
    flex-shrink: 0;
}

/* PIN Not Set State */
.pin-empty-state-premium {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 20px;
    background: rgba(231, 74, 59, 0.02);
    border-radius: 8px;
    border: 1px dashed rgba(231, 74, 59, 0.3);
}

.pin-empty-icon-premium {
    width: 60px;
    height: 60px;
    background: rgba(231, 74, 59, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--danger);
    font-size: 24px;
    margin-right: 20px;
    flex-shrink: 0;
}

.pin-empty-content-premium {
    flex: 1;
}

.pin-empty-title-premium {
    font-size: 16px;
    font-weight: 600;
    color: var(--danger);
    margin: 0 0 5px 0;
}

.pin-empty-description-premium {
    font-size: 13px;
    color: var(--secondary);
    margin: 0;
    line-height: 1.4;
}

.pin-setup-action-premium {
    text-align: center;
    margin-bottom: 20px;
}

.pin-setup-btn-premium {
    display: inline-flex;
    align-items: center;
    padding: 12px 24px;
    background: linear-gradient(135deg, var(--primary), #6c5ce7);
    color: white;
    border: none;
    border-radius: 25px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(78, 115, 223, 0.3);
    text-decoration: none;
}

.pin-setup-btn-premium:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(78, 115, 223, 0.4);
    color: white;
}

.pin-setup-btn-premium i {
    margin-right: 8px;
    font-size: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .pin-header-premium {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .pin-display-section-premium {
        flex-direction: column;
        gap: 15px;
    }

    .pin-actions-premium {
        margin-left: 0;
        justify-content: center;
        width: 100%;
    }

    .pin-action-btn-premium {
        width: 70px;
        height: 70px;
    }

    .pin-empty-state-premium {
        flex-direction: column;
        text-align: center;
    }

    .pin-empty-icon-premium {
        margin-right: 0;
        margin-bottom: 15px;
    }
}

/* User Info Items */
.user-info-item {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.user-info-item:last-child {
    border-bottom: none;
}

.user-info-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: rgba(78, 115, 223, 0.1);
    color: var(--primary);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
}

.user-info-content {
    flex: 1;
}

.user-info-label {
    font-size: 12px;
    color: var(--secondary);
    margin-bottom: 2px;
}

.user-info-value {
    font-weight: 500;
}

/* Stats Cards */
.stat-card-modern {
    display: flex;
    align-items: center;
    padding: 15px;
    border-radius: 10px;
    background-color: var(--light);
    transition: all 0.3s ease;
}

.stat-card-modern:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.stat-icon {
    width: 45px;
    height: 45px;
    border-radius: 10px;
    background-color: rgba(78, 115, 223, 0.1);
    color: var(--primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    margin-right: 15px;
}

.stat-content {
    flex: 1;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    line-height: 1.2;
}

.stat-label {
    font-size: 0.8rem;
    color: var(--secondary);
}

/* Physical Stats Card */
.physical-stats-card {
    overflow: hidden;
    transition: all 0.3s ease;
}

.physical-stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
}

.bg-gradient-primary {
    background: linear-gradient(45deg, var(--primary), #6f8cff);
}

.bmi-badge {
    padding: 6px 12px;
    border-radius: 20px;
    color: white;
    font-weight: 500;
    font-size: 0.8rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* BMI Gauge */
.bmi-gauge-container {
    padding: 5px 0;
    margin-bottom: 15px;
}

.bmi-gauge {
    position: relative;
    height: 50px;
    margin: 15px 0;
    padding: 0 5px;
}

.bmi-gauge-scale {
    display: flex;
    height: 8px;
    border-radius: 4px;
    overflow: hidden;
}

.bmi-gauge-section {
    height: 100%;
    flex: 1;
    position: relative;
}

.bmi-gauge-section:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    top: -25px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.65rem;
    white-space: nowrap;
    z-index: 10;
}

.bmi-gauge-indicator {
    position: absolute;
    top: -8px;
    width: 2px;
    height: 24px;
    background-color: #333;
    transform: translateX(-50%);
    transition: left 0.5s ease;
}

.bmi-gauge-indicator::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 8px;
    height: 8px;
    background-color: #333;
    border-radius: 50%;
}

.bmi-gauge-indicator-value {
    position: absolute;
    top: -22px;
    left: 50%;
    transform: translateX(-50%);
    background-color: #333;
    color: white;
    padding: 1px 5px;
    border-radius: 3px;
    font-size: 0.7rem;
    font-weight: bold;
    white-space: nowrap;
}

.bmi-gauge-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    padding: 0 5px;
}

.bmi-gauge-labels span {
    font-size: 0.65rem;
    color: var(--secondary);
    position: relative;
}

.bmi-gauge-labels span::before {
    content: '';
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    width: 1px;
    height: 6px;
    background-color: rgba(0, 0, 0, 0.2);
}

.bmi-category {
    margin-top: 5px;
}

/* BMI Indicator Colors */
.bmi-underweight {
    background-color: var(--info);
}

.bmi-normal {
    background-color: var(--success);
}

.bmi-overweight {
    background-color: var(--warning);
}

.bmi-obese {
    background-color: var(--danger);
}

/* Physical Stats Grid */
.physical-stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-top: 10px;
}

@media (max-width: 1400px) {
    .physical-stats-grid {
        grid-template-columns: 1fr;
    }
}

.physical-stat-item {
    display: flex;
    align-items: center;
    padding: 12px;
    border-radius: 10px;
    background-color: var(--light);
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.physical-stat-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.physical-stat-icon {
    min-width: 40px;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: linear-gradient(45deg, rgba(78, 115, 223, 0.1), rgba(78, 115, 223, 0.2));
    color: var(--primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    margin-right: 12px;
    flex-shrink: 0;
}

.physical-stat-info {
    flex: 1;
    min-width: 0; /* Allows text to truncate properly */
}

.physical-stat-label {
    font-size: 0.75rem;
    color: var(--secondary);
    margin-bottom: 3px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.physical-stat-value {
    display: flex;
    align-items: baseline;
    flex-wrap: wrap;
}

.physical-stat-value .value {
    font-size: 1.1rem;
    font-weight: bold;
    line-height: 1.2;
    margin-right: 5px;
}

.physical-stat-value .unit {
    font-size: 0.75rem;
    color: var(--secondary);
}

.physical-stat-value .not-available {
    font-size: 0.85rem;
    color: var(--secondary);
    font-style: italic;
}

/* Access List */
.access-list {
    padding: 0;
}

.access-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.access-item:last-child {
    border-bottom: none;
}

.access-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
}

.bg-primary-light {
    background-color: rgba(78, 115, 223, 0.1);
}

.bg-success-light {
    background-color: rgba(28, 200, 138, 0.1);
}

.bg-info-light {
    background-color: rgba(54, 185, 204, 0.1);
}

.bg-warning-light {
    background-color: rgba(246, 194, 62, 0.1);
}

.access-content {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.access-title {
    font-weight: 500;
}



/* Verification Status */
.verification-status {
    padding: 15px;
    border-radius: 10px;
    background-color: var(--light);
}

.verification-badge {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.verification-badge.active {
    background-color: var(--success);
}

.verification-badge.expired {
    background-color: var(--danger);
}

.verification-code {
    font-size: 1.2rem;
    letter-spacing: 1px;
}

.verification-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: rgba(78, 115, 223, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Course List */
.course-list {
    padding: 0;
}

.course-item {
    padding: 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.course-item:last-child {
    border-bottom: none;
}

.course-avatar {
    width: 45px;
    height: 45px;
    border-radius: 10px;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 18px;
}

/* Workout List */
.workout-list {
    padding: 0;
}

.workout-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.workout-item:last-child {
    border-bottom: none;
}

.workout-avatar {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
}

.workout-content {
    flex: 1;
    margin-left: 15px;
}

.workout-title {
    font-weight: 500;
    margin-bottom: 5px;
}

.workout-meta {
    display: flex;
    font-size: 12px;
    color: var(--secondary);
}

.workout-duration {
    margin-right: 15px;
}

.workout-stats {
    text-align: center;
    margin-left: 15px;
}

.workout-stat-value {
    font-weight: bold;
    color: var(--danger);
}

.workout-stat-label {
    font-size: 12px;
    color: var(--secondary);
}

/* Empty State */
.empty-state {
    padding: 20px;
    text-align: center;
}

.empty-state-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background-color: var(--light);
    color: var(--secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin: 0 auto;
}

/* Daily Total Badge */
.badge.bg-danger.rounded-pill {
    font-size: 0.85rem;
    font-weight: 600;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
}

.badge.bg-danger.rounded-pill:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}
</style>

<!-- Change PIN Modal -->
<div class="modal fade" id="changePinModal" tabindex="-1" aria-labelledby="changePinModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <form id="changePinForm">
        <div class="modal-header">
          <h5 class="modal-title" id="changePinModalLabel">Change App Login PIN</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="mb-3">
            <label class="form-label">Current PIN</label>
            <div class="input-group">
              <input type="text" class="form-control" id="currentPinField" value="<?php echo !empty($user['pin']) && (!isset($user['pin_expires_at']) || strtotime($user['pin_expires_at']) > time()) ? htmlspecialchars($user['pin']) : '' ?>" readonly>
              <button class="btn btn-outline-secondary" type="button" id="copyPinBtn">Copy</button>
            </div>
            <div class="form-text" id="pinExpiryText">
              <?php if (!empty($user['pin']) && !empty($user['pin_expires_at']) && strtotime($user['pin_expires_at']) > time()): ?>
                Expires at: <?php echo date('M d, Y h:i A', strtotime($user['pin_expires_at'])); ?>
              <?php elseif (!empty($user['pin'])): ?>
                <span class="text-danger">PIN expired. Please set a new PIN.</span>
              <?php endif; ?>
            </div>
          </div>
          <div class="mb-3">
            <label for="newPin" class="form-label">New 4-digit PIN</label>
            <div class="input-group">
              <input type="text" class="form-control" id="newPin" name="newPin" maxlength="4" pattern="\d{4}" required autocomplete="off" inputmode="numeric">
              <button class="btn btn-outline-secondary" type="button" id="generatePinBtn">Generate</button>
            </div>
            <div class="form-text">Enter or generate a new 4-digit PIN (numbers only).</div>
          </div>
          <div class="mb-3">
            <label for="confirmPin" class="form-label">Confirm New PIN</label>
            <input type="text" class="form-control" id="confirmPin" name="confirmPin" maxlength="4" pattern="\d{4}" required autocomplete="off" inputmode="numeric">
          </div>
          <div id="pinChangeMsg" class="text-danger small"></div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="submit" class="btn btn-primary">Save PIN</button>
        </div>
      </form>
    </div>
  </div>
</div>

<script>
// Generate random 4-digit PIN
function generateRandomPin() {
    return ('' + Math.floor(1000 + Math.random() * 9000));
}

function showPinExpiry(durationMinutes = 30) {
    const now = new Date();
    const expiry = new Date(now.getTime() + durationMinutes * 60000);
    document.getElementById('pinExpiryText').innerHTML = 'Expires at: ' + expiry.toLocaleString() + '<br><span class="text-muted">(Valid for ' + durationMinutes + ' minutes)</span>';
}

document.addEventListener('DOMContentLoaded', function() {
    // Set up generate button
    const generateBtn = document.getElementById('generatePinBtn');
    generateBtn.setAttribute('aria-label', 'Generate new PIN');
    generateBtn.addEventListener('click', function() {
        generateBtn.disabled = true;
        var pin = generateRandomPin();
        document.getElementById('newPin').value = pin;
        document.getElementById('confirmPin').value = pin;
        showPinExpiry();
        setTimeout(()=>{generateBtn.disabled = false;}, 500); // Re-enable after short delay
    });
    // Set up copy button
    const copyBtn = document.getElementById('copyPinBtn');
    copyBtn.setAttribute('aria-label', 'Copy current PIN');
    copyBtn.addEventListener('click', function() {
        var pin = document.getElementById('currentPinField').value;
        if (pin) {
            navigator.clipboard.writeText(pin);
            this.innerText = 'Copied!';
            setTimeout(()=>this.innerText='Copy', 1200);
        }
    });
    // When modal opens, if no PIN or expired, auto-generate
    var changePinModal = document.getElementById('changePinModal');
    changePinModal.addEventListener('show.bs.modal', function() {
        var currentPin = document.getElementById('currentPinField').value;
        var expiryText = document.getElementById('pinExpiryText').innerText;
        if (!currentPin || expiryText.includes('expired')) {
            var pin = generateRandomPin();
            document.getElementById('newPin').value = pin;
            document.getElementById('confirmPin').value = pin;
            showPinExpiry();
        } else {
            document.getElementById('newPin').value = '';
            document.getElementById('confirmPin').value = '';
        }
        document.getElementById('newPin').focus();
    });
    // Enforce numeric-only input for PIN fields
    const newPinField = document.getElementById('newPin');
    const confirmPinField = document.getElementById('confirmPin');
    [newPinField, confirmPinField].forEach(function(field) {
        field.addEventListener('input', function() {
            this.value = this.value.replace(/[^\d]/g, '').slice(0, 4);
        });
    });
});

document.getElementById('changePinForm').addEventListener('submit', function(e) {
    e.preventDefault();
    var newPin = document.getElementById('newPin').value.trim().replace(/[^\d]/g, '');
    var confirmPin = document.getElementById('confirmPin').value.trim().replace(/[^\d]/g, '');
    var msg = document.getElementById('pinChangeMsg');
    msg.textContent = '';
    if (newPin.length !== 4) {
        msg.textContent = 'PIN must be exactly 4 digits (numbers only, no spaces).';
        document.getElementById('newPin').focus();
        return;
    }
    if (newPin !== confirmPin) {
        msg.textContent = 'PINs do not match.';
        document.getElementById('confirmPin').focus();
        return;
    }
    // AJAX request to update PIN
    var xhr = new XMLHttpRequest();
    xhr.open('POST', 'user_update_pin.php', true);
    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            if (xhr.status === 200) {
                try {
                    var res = JSON.parse(xhr.responseText);
                    if (res.success) {
                        msg.classList.remove('text-danger');
                        msg.classList.add('text-success');
                        msg.textContent = 'PIN updated successfully!';

                        // Update the new PIN display
                        const pinActualValue = document.getElementById('pin-actual-value');
                        if (pinActualValue) {
                            pinActualValue.textContent = res.pin;
                        }

                        // Update the modal field
                        const currentPinField = document.getElementById('currentPinField');
                        if (currentPinField) {
                            currentPinField.value = res.pin;
                        }
                        document.getElementById('pinExpiryText').innerHTML = 'Expires at: ' + new Date(res.expires_at.replace(' ', 'T')).toLocaleString();
                        setTimeout(function() {
                            var modal = bootstrap.Modal.getInstance(document.getElementById('changePinModal'));
                            modal.hide();
                            msg.textContent = '';
                        }, 1200);
                    } else {
                        msg.classList.remove('text-success');
                        msg.classList.add('text-danger');
                        msg.textContent = res.message || 'Failed to update PIN.';
                    }
                } catch (e) {
                    msg.classList.remove('text-success');
                    msg.classList.add('text-danger');
                    msg.textContent = 'Failed to update PIN.';
                }
            } else {
                msg.classList.remove('text-success');
                msg.classList.add('text-danger');
                msg.textContent = 'Server error.';
            }
        }
    };
    xhr.send('user_id=<?php echo $userId; ?>&new_pin=' + encodeURIComponent(newPin));
});
</script>

<style>
.timeline {
    position: relative;
    padding: 1rem 0;
}

.timeline-item {
    position: relative;
    padding-left: 2rem;
    margin-bottom: 1.5rem;
}

.timeline-marker {
    position: absolute;
    left: 0;
    top: 0;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #4e73df;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #4e73df;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: 5px;
    top: 12px;
    height: calc(100% + 0.5rem);
    width: 2px;
    background: #e3e6f0;
}

.timeline-content {
    background: #f8f9fc;
    padding: 1rem;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}
</style>

<?php require_once 'includes/footer.php'; ?>
