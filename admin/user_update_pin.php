<?php
require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/auth.php';
header('Content-Type: application/json');

$auth = new Auth();
if (!$auth->isLoggedIn() || (!$auth->hasRole('admin') && !$auth->hasRole('super_admin') && !$auth->hasPermission('manage_users'))) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

$userId = isset($_POST['user_id']) ? (int)$_POST['user_id'] : 0;
$newPin = isset($_POST['new_pin']) ? trim($_POST['new_pin']) : '';

if (!$userId || !preg_match('/^\d{4}$/', $newPin)) {
    echo json_encode(['success' => false, 'message' => 'Invalid input.']);
    exit;
}

$expiresAt = date('Y-m-d H:i:s', strtotime('+30 minutes'));
$db = new Database();
$conn = $db->getConnection();
$stmt = $conn->prepare('UPDATE users SET pin = ?, pin_expires_at = ? WHERE id = ?');
$stmt->bind_param('ssi', $newPin, $expiresAt, $userId);
if ($stmt->execute()) {
    echo json_encode(['success' => true, 'pin' => $newPin, 'expires_at' => $expiresAt]);
} else {
    echo json_encode(['success' => false, 'message' => 'Failed to update PIN.']);
} 
