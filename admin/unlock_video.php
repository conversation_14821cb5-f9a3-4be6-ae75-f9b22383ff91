<?php
require_once 'includes/config.php';
require_once 'includes/auth.php';
require_once 'includes/database.php';
require_once 'includes/utilities.php';

// Initialize auth
$auth = new Auth();

// Check if user is logged in
if (!$auth->isLoggedIn()) {
    Utilities::setFlashMessage('error', 'You must be logged in to access this page.');
    Utilities::redirect('login.php');
    exit;
}

// Check if user has admin role
if (!$auth->hasRole('admin')) {
    Utilities::setFlashMessage('error', 'You do not have permission to access this page.');
    Utilities::redirect('index.php');
    exit;
}

// Check if request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    Utilities::setFlashMessage('error', 'Invalid request method.');
    Utilities::redirect('users.php');
    exit;
}

// Get parameters
$videoId = isset($_POST['video_id']) ? intval($_POST['video_id']) : 0;
$userId = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
$courseId = isset($_POST['course_id']) ? intval($_POST['course_id']) : 0;

// Validate parameters
if ($videoId <= 0 || $userId <= 0 || $courseId <= 0) {
    Utilities::setFlashMessage('error', 'Invalid parameters.');
    Utilities::redirect('users.php');
    exit;
}

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Check if video exists and belongs to the course
$videoQuery = "SELECT * FROM course_videos WHERE id = ? AND course_id = ?";
$videoStmt = $conn->prepare($videoQuery);
$videoStmt->bind_param("ii", $videoId, $courseId);
$videoStmt->execute();
$videoResult = $videoStmt->get_result();

if ($videoResult->num_rows === 0) {
    Utilities::setFlashMessage('error', 'Video not found or does not belong to the specified course.');
    Utilities::redirect("video_analytics.php?user_id=$userId&course_id=$courseId");
    exit;
}

$video = $videoResult->fetch_assoc();

// Check if user is enrolled in the course
$enrollmentQuery = "SELECT * FROM user_course_enrollments WHERE user_id = ? AND course_id = ?";
$enrollmentStmt = $conn->prepare($enrollmentQuery);
$enrollmentStmt->bind_param("ii", $userId, $courseId);
$enrollmentStmt->execute();
$enrollmentResult = $enrollmentStmt->get_result();

if ($enrollmentResult->num_rows === 0) {
    Utilities::setFlashMessage('error', 'User is not enrolled in this course.');
    Utilities::redirect("video_analytics.php?user_id=$userId&course_id=$courseId");
    exit;
}

// Check if video is already unlocked
$progressQuery = "SELECT * FROM user_video_progress WHERE user_id = ? AND video_id = ?";
$progressStmt = $conn->prepare($progressQuery);
$progressStmt->bind_param("ii", $userId, $videoId);
$progressStmt->execute();
$progressResult = $progressStmt->get_result();

if ($progressResult->num_rows > 0) {
    $progress = $progressResult->fetch_assoc();
    
    if ($progress['is_unlocked']) {
        Utilities::setFlashMessage('info', 'Video is already unlocked for this user.');
        Utilities::redirect("video_analytics.php?user_id=$userId&course_id=$courseId");
        exit;
    }
    
    // Update existing progress record
    $updateQuery = "UPDATE user_video_progress SET is_unlocked = 1, unlock_date = NOW() WHERE user_id = ? AND video_id = ?";
    $updateStmt = $conn->prepare($updateQuery);
    $updateStmt->bind_param("ii", $userId, $videoId);
    
    if ($updateStmt->execute()) {
        Utilities::setFlashMessage('success', 'Video has been unlocked successfully.');
    } else {
        Utilities::setFlashMessage('error', 'Failed to unlock video: ' . $conn->error);
    }
} else {
    // Insert new progress record
    $insertQuery = "INSERT INTO user_video_progress (user_id, video_id, is_unlocked, unlock_date) VALUES (?, ?, 1, NOW())";
    $insertStmt = $conn->prepare($insertQuery);
    $insertStmt->bind_param("ii", $userId, $videoId);
    
    if ($insertStmt->execute()) {
        Utilities::setFlashMessage('success', 'Video has been unlocked successfully.');
    } else {
        Utilities::setFlashMessage('error', 'Failed to unlock video: ' . $conn->error);
    }
}

// Log activity
$activityQuery = "INSERT INTO user_activity_log (user_id, activity_type, related_id, details, created_at) VALUES (?, 'admin_unlock', ?, ?, NOW())";
$details = json_encode([
    'admin_id' => $auth->getUserId(),
    'video_id' => $videoId,
    'course_id' => $courseId
]);
$activityStmt = $conn->prepare($activityQuery);
$activityStmt->bind_param("iis", $userId, $videoId, $details);
$activityStmt->execute();

// Redirect back to video analytics page
Utilities::redirect("video_analytics.php?user_id=$userId&course_id=$courseId");
exit;
